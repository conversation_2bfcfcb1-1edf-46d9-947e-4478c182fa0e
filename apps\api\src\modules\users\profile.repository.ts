import { db, schema } from '@needit/db';
import { eq, count, desc, asc, sql } from 'drizzle-orm';

import type { UserSelectDto } from './user.dto';

export const profileRepository = {
  /**
   * Get user activity statistics
   */
  async getUserStats(userId: UserSelectDto['id']) {
    // Get count of user's needs
    const needsCount = await db
      .select({ count: count() })
      .from(schema.needs)
      .where(eq(schema.needs.userId, userId))
      .then((res) => res[0]?.count || 0);

    // Get count of user's offers
    const offersCount = await db
      .select({ count: count() })
      .from(schema.offers)
      .where(eq(schema.offers.userId, userId))
      .then((res) => res[0]?.count || 0);

    // Get count of user's messages
    const messagesCount = await db
      .select({ count: count() })
      .from(schema.messages)
      .where(eq(schema.messages.senderId, userId))
      .then((res) => res[0]?.count || 0);

    return {
      needsCount,
      offersCount,
      messagesCount,
    };
  },

  /**
   * Get user's recent needs
   */
  async getRecentNeeds(userId: UserSelectDto['id'], limit = 5) {
    return await db
      .select()
      .from(schema.needs)
      .where(eq(schema.needs.userId, userId))
      .orderBy(desc(schema.needs.createdAt))
      .limit(limit);
  },

  /**
   * Get user's recent offers
   */
  async getRecentOffers(userId: UserSelectDto['id'], limit = 5) {
    return await db
      .select()
      .from(schema.offers)
      .where(eq(schema.offers.userId, userId))
      .orderBy(desc(schema.offers.createdAt))
      .limit(limit);
  },

  /**
   * Get user's recent messages
   */
  async getRecentMessages(userId: UserSelectDto['id'], limit = 5) {
    return await db
      .select()
      .from(schema.messages)
      .where(eq(schema.messages.senderId, userId))
      .orderBy(desc(schema.messages.createdAt))
      .limit(limit);
  },

  /**
   * Get user's activity timeline (combined recent needs, offers, and messages)
   */
  async getActivityTimeline(userId: UserSelectDto['id'], limit = 10) {
    // This is a simplified implementation
    // In a real application, you might want to use a more sophisticated approach
    // to combine different types of activities into a single timeline
    
    const recentNeeds = await this.getRecentNeeds(userId, limit);
    const recentOffers = await this.getRecentOffers(userId, limit);
    const recentMessages = await this.getRecentMessages(userId, limit);

    // Combine and sort by creation date
    const timeline = [
      ...recentNeeds.map(need => ({ 
        type: 'need', 
        data: need, 
        createdAt: need.createdAt 
      })),
      ...recentOffers.map(offer => ({ 
        type: 'offer', 
        data: offer, 
        createdAt: offer.createdAt 
      })),
      ...recentMessages.map(message => ({ 
        type: 'message', 
        data: message, 
        createdAt: message.createdAt 
      })),
    ].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
     .slice(0, limit);

    return timeline;
  }
};
