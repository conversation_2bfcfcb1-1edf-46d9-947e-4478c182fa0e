import React from 'react';
import { NavigationContainer } from '@react-navigation/native';

// NavigationProvider that provides context for React Navigation components
// while being compatible with Expo Router
export default function NavigationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <NavigationContainer
      linking={undefined} // Don't handle linking, let Expo Router do it
      theme={undefined} // Use default theme
    >
      {children}
    </NavigationContainer>
  );
}
