import { db, schema } from '@needit/db';
import { eq, and, getTableColumns } from 'drizzle-orm';
import { jsonBuildObject } from '@/shared/lib';

import type { MeetingProposalCreateDto } from './meeting-proposal.dto';

export const meetingProposalRepository = {
  async create(data: MeetingProposalCreateDto & { proposerId: string }) {
    return await db
      .insert(schema.meetingProposals)
      .values(data)
      .returning()
      .then((res) => res[0]);
  },

  async findById(id: string) {
    return await db
      .select({
        ...getTableColumns(schema.meetingProposals),
        proposer: jsonBuildObject({
          id: schema.users.id,
          firstName: schema.users.firstName,
          lastName: schema.users.lastName,
          email: schema.users.email,
        }),
        receiver: jsonBuildObject({
          id: schema.users.id,
          firstName: schema.users.firstName,
          lastName: schema.users.lastName,
          email: schema.users.email,
        }),
      })
      .from(schema.meetingProposals)
      .leftJoin(
        schema.users,
        eq(schema.meetingProposals.proposerId, schema.users.id)
      )
      .leftJoin(
        schema.users,
        eq(schema.meetingProposals.receiverId, schema.users.id)
      )
      .where(eq(schema.meetingProposals.id, id))
      .then((res) => res[0]);
  },

  async findByOfferId(offerId: string) {
    return await db
      .select({
        ...getTableColumns(schema.meetingProposals),
        proposer: jsonBuildObject({
          id: schema.users.id,
          firstName: schema.users.firstName,
          lastName: schema.users.lastName,
          email: schema.users.email,
        }),
      })
      .from(schema.meetingProposals)
      .leftJoin(
        schema.users,
        eq(schema.meetingProposals.proposerId, schema.users.id)
      )
      .where(eq(schema.meetingProposals.offerId, offerId))
      .orderBy(schema.meetingProposals.createdAt);
  },

  async findActiveByOfferId(offerId: string) {
    return await db
      .select()
      .from(schema.meetingProposals)
      .where(
        and(
          eq(schema.meetingProposals.offerId, offerId),
          eq(schema.meetingProposals.status, 'pending')
        )
      );
  },

  async updateStatus(
    id: string,
    status: 'accepted' | 'declined',
    qrCodeData?: string
  ) {
    const updateData: any = {
      status,
      ...(status === 'accepted' && { acceptedAt: new Date() }),
      ...(status === 'declined' && { declinedAt: new Date() }),
      ...(qrCodeData && { qrCodeData }),
    };

    return await db
      .update(schema.meetingProposals)
      .set(updateData)
      .where(eq(schema.meetingProposals.id, id))
      .returning()
      .then((res) => res[0]);
  },

  async findByUserAndOffer(userId: string, offerId: string) {
    return await db
      .select()
      .from(schema.meetingProposals)
      .where(
        and(
          eq(schema.meetingProposals.offerId, offerId),
          and(
            eq(schema.meetingProposals.proposerId, userId),
            eq(schema.meetingProposals.receiverId, userId)
          )
        )
      );
  },

  async expireOldProposals() {
    return await db
      .update(schema.meetingProposals)
      .set({
        status: 'expired',
      })
      .where(
        and(
          eq(schema.meetingProposals.status, 'pending')
          // Expires after 24 hours if no expiresAt set
          // or if expiresAt is past
        )
      )
      .returning();
  },
};
