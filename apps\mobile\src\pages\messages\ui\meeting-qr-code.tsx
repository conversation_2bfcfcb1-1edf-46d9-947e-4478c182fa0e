import React, { useMemo } from 'react';
import { Text, VStack, HStack, Box, Icon } from '@/shared/ui';
import { Calendar, Clock, CheckCircle, QrCode } from 'lucide-react-native';
import QRCode from 'react-native-qrcode-svg';

interface MeetingQRCodeProps {
  meetingId: string;
  meetingTime: string;
  timestamp: string;
  userRole?: 'need_creator' | 'offer_creator';
  createdAt?: string;
}

export const MeetingQRCode = React.memo(
  ({
    meetingId,
    meetingTime,
    timestamp,
    userRole = 'need_creator',
    createdAt,
  }: MeetingQRCodeProps) => {
    // Générer les données du QR code de manière stable
    const meetingData = useMemo(() => {
      return JSON.stringify({
        id: meetingId,
        time: meetingTime,
        role: userRole,
        created: createdAt || new Date().toISOString(),
      });
    }, [meetingId, meetingTime, userRole, createdAt]);

    // NeedIt theme colors based on user role - mémorisé pour éviter la recréation
    const themeColors = useMemo(() => {
      const isNeedCreator = userRole === 'need_creator';
      return {
        primary: isNeedCreator ? '#3B82F6' : '#10B981', // NeedIt Blue : NeedIt Green
        background: isNeedCreator ? '#EFF6FF' : '#ECFDF5', // Light blue : Light green
        border: isNeedCreator ? '#DBEAFE' : '#D1FAE5', // Blue border : Green border
        text: isNeedCreator ? '#1D4ED8' : '#059669', // Darker blue : Darker green
      };
    }, [userRole]);

    const roleMessages = useMemo(() => {
      const isNeedCreator = userRole === 'need_creator';
      return {
        title: isNeedCreator ? 'Your Need Meeting' : 'Your Offer Meeting',
        subtitle: isNeedCreator
          ? 'Show this QR code to verify you created this need'
          : 'Show this QR code to verify you made this offer',
      };
    }, [userRole]);

    return (
      <VStack className="mb-4 self-center w-full max-w-sm" space="sm">
        {/* Main Card */}
        <Box className="bg-white rounded-3xl shadow-lg border border-gray-100 overflow-hidden">
          {/* Header Section */}
          <Box
            className="px-6 py-4"
            style={{ backgroundColor: themeColors.primary }}
          >
            <VStack space="xs">
              <HStack className="justify-between items-center">
                <HStack className="items-center" space="xs">
                  <Icon as={CheckCircle} size="md" color="#FFFFFF" />
                  <Text className="text-white font-semibold" size="lg">
                    {roleMessages.title}
                  </Text>
                </HStack>
                <HStack
                  className="items-center bg-white/20 rounded-full px-3 py-1"
                  space="xs"
                >
                  <Icon as={Clock} size="sm" color="#FFFFFF" />
                  <Text className="text-white font-medium" size="sm">
                    {meetingTime}
                  </Text>
                </HStack>
              </HStack>
              <Text className="text-white/90 font-medium" size="sm">
                Meeting Confirmed ✓
              </Text>
            </VStack>
          </Box>

          {/* QR Code Section */}
          <VStack className="px-8 py-8 items-center" space="lg">
            {/* QR Code Container */}
            <Box
              className="rounded-2xl p-6 shadow-sm border-2"
              style={{
                backgroundColor: themeColors.background,
                borderColor: themeColors.border,
              }}
            >
              <QRCode
                value={meetingData}
                size={160}
                color={themeColors.primary}
                backgroundColor="white"
                logoSize={30}
              />
            </Box>

            {/* Instructions */}
            <VStack className="items-center" space="sm">
              <HStack className="items-center" space="xs">
                <Icon
                  as={QrCode}
                  size="md"
                  style={{ color: themeColors.text }}
                />
                <Text
                  className="font-bold"
                  size="lg"
                  style={{ color: themeColors.text }}
                >
                  Verification QR Code
                </Text>
              </HStack>
              <Text
                className="text-center font-medium leading-5"
                size="sm"
                style={{ color: themeColors.text }}
              >
                {roleMessages.subtitle}
              </Text>
              <Box
                className="rounded-full px-4 py-2 mt-2"
                style={{ backgroundColor: themeColors.background }}
              >
                <Text
                  className="font-semibold"
                  size="xs"
                  style={{ color: themeColors.text }}
                >
                  ID: {meetingId.slice(0, 8)}...
                </Text>
              </Box>
            </VStack>
          </VStack>
        </Box>

        {/* Timestamp */}
        <Text className="text-gray-500 text-center font-medium" size="xs">
          Generated: {timestamp}
        </Text>
      </VStack>
    );
  }
);
