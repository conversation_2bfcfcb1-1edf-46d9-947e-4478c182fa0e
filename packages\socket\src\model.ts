export interface ServerToClientEvents {
  newOffer: ({
    offerId,
    needId,
    needTitle,
    userId,
  }: {
    offerId: string;
    needId: string;
    needTitle: string;
    userId: string;
  }) => void;
  newMessage: ({
    senderFirstName,
    senderLastName,
    content,
  }: {
    senderFirstName: string;
    senderLastName: string;
    content: string;
  }) => void;
  newMeetingProposal: ({
    proposalId,
    offerId,
    proposerId,
    receiverId,
    proposedMeetingTime,
    message,
  }: {
    proposalId: string;
    offerId: string;
    proposerId: string;
    receiverId: string;
    proposedMeetingTime: string;
    message?: string;
  }) => void;
  meetingProposalAccepted: ({
    proposalId,
    offerId,
    acceptedBy,
    qrCodeData,
  }: {
    proposalId: string;
    offerId: string;
    acceptedBy: string;
    qrCodeData: string;
  }) => void;
  meetingProposalDeclined: ({
    proposalId,
    offerId,
    declinedBy,
  }: {
    proposalId: string;
    offerId: string;
    declinedBy: string;
  }) => void;
}

export interface ClientToServerEvents {
  typing: ({
    senderId,
    receiverId,
  }: {
    senderId: string;
    receiverId: string;
  }) => void;
}
