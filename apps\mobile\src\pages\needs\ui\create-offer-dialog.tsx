import { zodResolver } from '@hookform/resolvers/zod';
import { createOfferSchema } from '@/pages/needs';
import { useOfferStore } from '@/shared/model';
import { Platform } from 'react-native';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogBody,
  Button,
  ButtonText,
  Text,
  AlertDialogBackdrop,
  AlertDialogHeader,
  Heading,
  VStack,
  Box,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  AlertCircleIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
} from '@/shared/ui';

import React from 'react';
import { Controller, useForm } from 'react-hook-form';

interface Props {
  needId: string;
}

export function CreateOfferDialog({ needId }: Props) {
  const { addOffer, isLoading } = useOfferStore();
  const [showAlertDialog, setShowAlertDialog] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleClose = () => {
    setShowAlertDialog(false);
    setError(null);
  };

  const form = useForm({
    resolver: zodResolver(createOfferSchema),
    defaultValues: {
      price: 0,
      message: '',
    },
  });

  const handleCreation = async () => {
    setError(null);
    const { price, message } = form.getValues();

    try {
      await addOffer({ price, message, needId });
      handleClose();
      // Reset form after successful submission
      form.reset();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create offer');
      console.error('Error creating offer:', err);
    }
  };

  return (
    <>
      {/* Fixed Bottom Button - Version plus compacte */}
      <Box
        className="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4"
        style={{
          zIndex: 1000,
          elevation: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          paddingTop: 8,
          paddingBottom: Platform.OS === 'ios' ? 24 : 8,
          height: Platform.OS === 'ios' ? 80 : 64, // Hauteur encore plus réduite
        }}
      >
        <Button
          onPress={() => setShowAlertDialog(true)}
          className="w-full bg-blue-600 rounded-xl shadow-lg"
          style={{
            backgroundColor: '#3B82F6',
            borderRadius: 12,
            height: 48, // Hauteur du bouton réduite
            shadowColor: '#3B82F6',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 4,
          }}
        >
          <ButtonText className="font-bold text-white text-base">
            Make an Offer
          </ButtonText>
        </Button>
      </Box>

      <AlertDialog isOpen={showAlertDialog} onClose={handleClose} size="lg">
        <AlertDialogBackdrop className="bg-black/40" />
        <AlertDialogContent className="bg-white rounded-3xl shadow-2xl border-0 m-4 min-h-[60%] max-h-[95%]">
          <AlertDialogHeader className="pb-6 pt-8 px-6">
            <VStack space="sm" className="items-center">
              <Heading
                className="text-gray-900 font-bold text-center"
                size="xl"
              >
                Make an Offer
              </Heading>
              <Text className="text-gray-500 text-center font-medium">
                Set your price and message for this need
              </Text>
            </VStack>
          </AlertDialogHeader>
          <AlertDialogBody className="px-6 pb-4">
            <VStack space="lg">
              <Controller
                control={form.control}
                name="price"
                render={({ field, fieldState }) => (
                  <FormControl
                    isInvalid={fieldState.error !== undefined}
                    size="md"
                  >
                    <FormControlLabel className="mb-3">
                      <FormControlLabelText className="text-gray-700 font-semibold text-base">
                        Price (€)
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input className="bg-gray-50 border-gray-200 rounded-xl h-14">
                      <InputField
                        type="text"
                        autoCapitalize="none"
                        placeholder="Enter amount (e.g., 10)"
                        className="text-gray-900 font-medium text-lg"
                        keyboardType="numeric"
                        {...field}
                        value={field.value.toString()}
                        onChangeText={(text) => {
                          // Filtrer pour n'accepter que les chiffres et le point décimal
                          const numericValue = text.replace(/[^0-9.]/g, '');
                          // S'assurer qu'il n'y a qu'un seul point décimal
                          const parts = numericValue.split('.');
                          const cleanValue =
                            parts.length > 2
                              ? parts[0] + '.' + parts.slice(1).join('')
                              : numericValue;
                          field.onChange(parseFloat(cleanValue) || 0);
                        }}
                      />
                    </Input>
                    <FormControlError>
                      <FormControlErrorIcon as={AlertCircleIcon} />
                      <FormControlErrorText className="text-red-500 font-medium">
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                )}
              />
              <Controller
                control={form.control}
                name="message"
                render={({ field, fieldState }) => (
                  <FormControl
                    isInvalid={fieldState.error !== undefined}
                    size="md"
                  >
                    <FormControlLabel className="mb-3">
                      <FormControlLabelText className="text-gray-700 font-semibold text-base">
                        Message
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input className="bg-gray-50 border-gray-200 rounded-xl h-20">
                      <InputField
                        type="text"
                        autoCapitalize="none"
                        className="text-gray-900 font-medium"
                        multiline={true}
                        numberOfLines={3}
                        textAlignVertical="top"
                        style={{
                          paddingTop: 12,
                          paddingBottom: 12,
                          lineHeight: 20,
                        }}
                        {...field}
                        onChangeText={field.onChange}
                      />
                    </Input>
                    <FormControlError>
                      <FormControlErrorIcon as={AlertCircleIcon} />
                      <FormControlErrorText className="text-red-500 font-medium">
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  </FormControl>
                )}
              />
            </VStack>
          </AlertDialogBody>
          <AlertDialogFooter className="px-6 pb-8 pt-6">
            {error && (
              <Box className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
                <Text className="text-red-700 font-medium text-center">
                  {error}
                </Text>
              </Box>
            )}
            <VStack space="sm" className="w-full">
              <Button
                className="bg-blue-600 rounded-xl h-14 shadow-lg shadow-blue-600/25"
                onPress={form.handleSubmit(handleCreation)}
                isDisabled={isLoading}
                style={{
                  backgroundColor: '#3B82F6',
                  borderRadius: 16,
                  height: 56,
                  shadowColor: '#3B82F6',
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 0.25,
                  shadowRadius: 6,
                  elevation: 6,
                }}
              >
                <ButtonText
                  className="font-bold text-base"
                  style={{
                    color: '#FFFFFF',
                    fontSize: 16,
                    fontWeight: 'bold',
                  }}
                >
                  {isLoading ? 'Creating Offer...' : 'Send Offer'}
                </ButtonText>
              </Button>
              <Button
                variant="outline"
                className="border-gray-300 rounded-xl h-12"
                onPress={handleClose}
                isDisabled={isLoading}
                style={{
                  borderColor: '#D1D5DB',
                  borderWidth: 2,
                  borderRadius: 16,
                  height: 48,
                  backgroundColor: 'transparent',
                }}
              >
                <ButtonText
                  className="font-semibold text-base"
                  style={{
                    color: '#4B5563',
                    fontSize: 16,
                    fontWeight: '600',
                  }}
                >
                  Cancel
                </ButtonText>
              </Button>
            </VStack>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
