﻿import React, { useState } from 'react';
import { View, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { useRouter } from 'expo-router';
import {
  Crown,
  Check,
  Star,
  Zap,
  Shield,
  Eye,
  Filter,
  MessageCircle,
  ChevronLeft,
} from 'lucide-react-native';

import { subscribe } from '@/pages/subscribe';
import {
  Box,
  Button,
  ButtonText,
  Heading,
  HStack,
  VStack,
  Text,
  Icon,
} from '@/shared/ui';

const pricingPlans = {
  monthly: {
    priceId: 'price_1RJElWRqAGveVCC5gEAH0asa', // À remplacer par le vrai ID Stripe
    price: '4.99',
    period: 'month',
    save: null,
  },
  yearly: {
    priceId: 'price_1RJEmsRqAGveVCC5jm6B18mD', // À remplacer par le vrai ID Stripe
    price: '49.99',
    period: 'year',
    save: '40%',
    monthlyEquivalent: '4.17',
  },
};

const premiumFeatures = [
  {
    icon: Filter,
    title: 'Advanced Filtering',
    description: 'Find exactly what you need with powerful search filters',
  },
  {
    icon: Eye,
    title: 'Enhanced Visibility',
    description: 'Your needs get priority placement and more views',
  },
  {
    icon: Zap,
    title: 'Priority Support',
    description: 'Get help faster with dedicated premium support',
  },
  {
    icon: Star,
    title: 'Exclusive Features',
    description: 'Access to beta features and premium-only tools',
  },
  {
    icon: Shield,
    title: 'Verified Badge',
    description: 'Stand out with a premium verification badge',
  },
];

export default function SubscribeScreen() {
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>(
    'yearly'
  );
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubscribe = async () => {
    setIsLoading(true);
    try {
      const data = await subscribe({
        priceId: pricingPlans[selectedPlan].priceId,
      });

      await Linking.openURL(data.url);
    } catch (error) {
      console.error('Subscription error:', error);
      alert('Une erreur est survenue. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pt-16 pb-4">
        <HStack className="items-center justify-between px-6">
          <TouchableOpacity
            onPress={() => router.back()}
            className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
          >
            <ChevronLeft size={20} color="#6B7280" />
          </TouchableOpacity>
          <Heading className="text-xl font-bold text-gray-900">
            NeedIt Premium
          </Heading>
          <View className="w-10" />
        </HStack>
      </Box>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <Box className="bg-white px-6 pb-8">
          <VStack className="items-center" space="lg">
            <Box className="w-20 h-20 bg-blue-100 rounded-3xl items-center justify-center">
              <Crown size={40} color="#3B82F6" />
            </Box>
            <VStack className="items-center" space="sm">
              <Heading className="text-3xl font-bold text-gray-900 text-center">
                Unlock Premium
              </Heading>
              <Text className="text-gray-600 text-center text-lg leading-relaxed max-w-sm">
                Get the most out of NeedIt with exclusive features and priority
                access
              </Text>
            </VStack>
          </VStack>
        </Box>

        {/* Pricing Cards */}
        <Box className="px-6 mb-8">
          <VStack space="sm">
            {/* Yearly Plan - Recommended */}
            <TouchableOpacity
              onPress={() => setSelectedPlan('yearly')}
              className={`relative rounded-2xl border-2 p-6 ${
                selectedPlan === 'yearly'
                  ? 'border-blue-600 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              {pricingPlans.yearly.save && (
                <Box className="absolute -top-3 left-6 bg-blue-600 px-3 py-1 rounded-full">
                  <Text className="text-white font-bold text-xs">
                    Save {pricingPlans.yearly.save}
                  </Text>
                </Box>
              )}

              <HStack className="justify-between items-center">
                <VStack space="xs">
                  <Text className="text-lg font-bold text-gray-900">
                    Annual Plan
                  </Text>
                  <HStack className="items-baseline" space="xs">
                    <Text className="text-3xl font-bold text-gray-900">
                      €{pricingPlans.yearly.price}
                    </Text>
                    <Text className="text-gray-600">/year</Text>
                  </HStack>
                  <Text className="text-sm text-gray-500">
                    €{pricingPlans.yearly.monthlyEquivalent}/month
                  </Text>
                </VStack>

                <Box
                  className={`w-6 h-6 rounded-full border-2 items-center justify-center ${
                    selectedPlan === 'yearly'
                      ? 'border-blue-600 bg-blue-600'
                      : 'border-gray-300'
                  }`}
                >
                  {selectedPlan === 'yearly' && (
                    <Check size={14} color="white" />
                  )}
                </Box>
              </HStack>
            </TouchableOpacity>

            {/* Monthly Plan */}
            <TouchableOpacity
              onPress={() => setSelectedPlan('monthly')}
              className={`rounded-2xl border-2 p-6 ${
                selectedPlan === 'monthly'
                  ? 'border-blue-600 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <HStack className="justify-between items-center">
                <VStack space="xs">
                  <Text className="text-lg font-bold text-gray-900">
                    Monthly Plan
                  </Text>
                  <HStack className="items-baseline" space="xs">
                    <Text className="text-3xl font-bold text-gray-900">
                      €{pricingPlans.monthly.price}
                    </Text>
                    <Text className="text-gray-600">/month</Text>
                  </HStack>
                </VStack>

                <Box
                  className={`w-6 h-6 rounded-full border-2 items-center justify-center ${
                    selectedPlan === 'monthly'
                      ? 'border-blue-600 bg-blue-600'
                      : 'border-gray-300'
                  }`}
                >
                  {selectedPlan === 'monthly' && (
                    <Check size={14} color="white" />
                  )}
                </Box>
              </HStack>
            </TouchableOpacity>
          </VStack>
        </Box>

        {/* Features */}
        <Box className="px-6 mb-8">
          <Heading className="text-xl font-bold text-gray-900 mb-6">
            What's included
          </Heading>
          <VStack space="md">
            {premiumFeatures.map((feature, index) => (
              <HStack key={index} className="items-start" space="sm">
                <Box className="w-10 h-10 bg-blue-100 rounded-xl items-center justify-center mt-1">
                  <Icon as={feature.icon} size="md" color="#3B82F6" />
                </Box>
                <VStack className="flex-1" space="xs">
                  <Text className="font-semibold text-gray-900">
                    {feature.title}
                  </Text>
                  <Text className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </Text>
                </VStack>
              </HStack>
            ))}
          </VStack>
        </Box>

        {/* Trust Section */}
        <Box className="bg-gray-100 mx-6 rounded-2xl p-6 mb-8">
          <VStack className="items-center" space="sm">
            <HStack className="items-center" space="xs">
              <Shield size={20} color="#10B981" />
              <Text className="font-semibold text-gray-900">
                Secure Payment
              </Text>
            </HStack>
            <Text className="text-center text-gray-600 text-sm">
              Powered by Stripe. Cancel anytime. No hidden fees.
            </Text>
          </VStack>
        </Box>
      </ScrollView>

      {/* Bottom CTA */}
      <Box className="bg-white border-t border-gray-100 px-6 py-4 pb-8">
        <Button
          onPress={handleSubscribe}
          disabled={isLoading}
          className="bg-blue-600 rounded-xl h-14 shadow-lg"
          style={{
            backgroundColor: '#3B82F6',
            shadowColor: '#3B82F6',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
        >
          <ButtonText className="font-bold text-white text-lg">
            {isLoading
              ? 'Processing...'
              : `Start Premium - €${pricingPlans[selectedPlan].price}/${pricingPlans[selectedPlan].period}`}
          </ButtonText>
        </Button>
        <Text className="text-center text-gray-500 text-sm mt-3">
          You can cancel anytime from your account settings
        </Text>
      </Box>
    </View>
  );
}
