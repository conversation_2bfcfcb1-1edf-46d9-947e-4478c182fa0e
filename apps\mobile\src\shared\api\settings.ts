import { nativeFetch } from '@/shared/lib';

export type UserSettings = {
  id: string;
  userId: string;
  language: string;
  darkMode: boolean;
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  profileVisibility: 'public' | 'friends' | 'private';
  locationSharing: boolean;
  createdAt: string;
  updatedAt: string;
};

export type NotificationSettings = {
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
};

export type PrivacySettings = {
  profileVisibility: 'public' | 'friends' | 'private';
  locationSharing: boolean;
};

export type AppearanceSettings = {
  darkMode: boolean;
  language: string;
};

export type SettingsUpdate = Partial<
  Omit<UserSettings, 'id' | 'userId' | 'createdAt' | 'updatedAt'>
>;

/**
 * Get user settings
 */
export async function getUserSettings() {
  const response = await nativeFetch('/api/settings', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get user settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Update user settings
 */
export async function updateSettings(settings: SettingsUpdate) {
  const response = await nativeFetch('/api/settings', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    throw new Error('Failed to update settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Update notification settings
 */
export async function updateNotificationSettings(
  settings: NotificationSettings
) {
  const response = await nativeFetch('/api/settings/notifications', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    throw new Error('Failed to update notification settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Update privacy settings
 */
export async function updatePrivacySettings(settings: PrivacySettings) {
  const response = await nativeFetch('/api/settings/privacy', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    throw new Error('Failed to update privacy settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Update appearance settings
 */
export async function updateAppearanceSettings(settings: AppearanceSettings) {
  const response = await nativeFetch('/api/settings/appearance', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    throw new Error('Failed to update appearance settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Reset settings to defaults
 */
export async function resetSettingsToDefaults() {
  const response = await nativeFetch('/api/settings/reset', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to reset settings');
  }

  const data = await response.json();
  return data.settings as UserSettings;
}

/**
 * Delete user settings
 */
export async function deleteUserSettings() {
  const response = await nativeFetch('/api/settings', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to delete settings');
  }

  const data = await response.json();
  return data.message;
}
