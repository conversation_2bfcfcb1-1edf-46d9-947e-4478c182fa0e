import { Link } from 'expo-router';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useState, useEffect, useRef, useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { signInSchema, SocialSignIn } from '@/pages/auth';
import { nativeFetch } from '@/shared/lib';
import {
  AlertCircleIcon,
  Button,
  ButtonText,
  EyeIcon,
  EyeOffIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  VStack,
  Box,
  Text,
  Spinner,
  HStack,
  Heading,
} from '@/shared/ui';

export function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [routerReady, setRouterReady] = useState(false);
  const isMountedRef = useRef(true);

  let router: any;
  try {
    const { useRouter } = require('expo-router');
    router = useRouter();
  } catch (error) {
    console.log('Router not ready yet');
  }

  const form = useForm<z.infer<typeof signInSchema>>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  useEffect(() => {
    const checkRouter = () => {
      try {
        if (router) {
          setRouterReady(true);
        }
      } catch (error) {
        setTimeout(checkRouter, 100);
      }
    };

    checkRouter();

    return () => {
      isMountedRef.current = false;
    };
  }, [router]);

  const safeSetLoading = useCallback((loading: boolean) => {
    if (isMountedRef.current) {
      setIsLoading(loading);
    }
  }, []);

  const handleSignIn = useCallback(async () => {
    if (!isMountedRef.current || !routerReady) return;

    Keyboard.dismiss();
    safeSetLoading(true);
    const { email, password } = form.getValues();

    try {
      const response = await nativeFetch('/api/auth/sign-in/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include',
      });

      if (!isMountedRef.current) return;

      if (response.ok) {
        if (router && routerReady) {
          router.push('/');
        }
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (error) {
      if (!isMountedRef.current) return;

      console.log(error);
      form.setError('password', {
        message: 'Invalid email or password. Please try again.',
      });
    } finally {
      safeSetLoading(false);
    }
  }, [router, routerReady, form, safeSetLoading]);

  const handleGoogleSignIn = useCallback(async () => {
    if (!isMountedRef.current) return;
    console.log('Google Sign In initiated');
  }, []);

  const handleAppleSignIn = useCallback(async () => {
    if (!isMountedRef.current) return;
    console.log('Apple Sign In initiated');
  }, []);

  if (!isMountedRef.current) {
    return null;
  }

  return (
    <Box className="px-6">
      <VStack space="lg" className="w-full max-w-sm mx-auto">
        {/* Main Form Card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <VStack space="lg">
            {/* Email Field */}
            <Controller
              control={form.control}
              name="email"
              render={({ field, fieldState }) => (
                <FormControl isInvalid={fieldState.error !== undefined}>
                  <Text className="text-sm font-semibold text-gray-700 mb-3">
                    Email
                  </Text>
                  <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                    <Input className="border-0 bg-transparent h-full">
                      <InputField
                        placeholder="<EMAIL>"
                        value={field.value}
                        onChangeText={field.onChange}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoComplete="email"
                        autoCorrect={false}
                        style={{
                          fontSize: 16,
                          lineHeight: 20,
                          height: 56,
                          paddingHorizontal: 16,
                          paddingVertical: 0,
                          textAlignVertical: 'center',
                          color: '#111827',
                          fontWeight: '500',
                        }}
                      />
                    </Input>
                  </Box>
                  {fieldState.error && (
                    <FormControlError className="mt-2">
                      <FormControlErrorIcon
                        as={AlertCircleIcon}
                        className="text-red-500"
                      />
                      <FormControlErrorText className="text-red-500 font-medium">
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            />

            {/* Password Field */}
            <Controller
              control={form.control}
              name="password"
              render={({ field, fieldState }) => (
                <FormControl isInvalid={fieldState.error !== undefined}>
                  <Text className="text-sm font-semibold text-gray-700 mb-3">
                    Password
                  </Text>
                  <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                    <Input className="border-0 bg-transparent h-full">
                      <InputField
                        placeholder="Enter your password"
                        value={field.value}
                        onChangeText={field.onChange}
                        secureTextEntry={!showPassword}
                        autoComplete="password"
                        autoCorrect={false}
                        style={{
                          fontSize: 16,
                          lineHeight: 20,
                          height: 56,
                          paddingHorizontal: 16,
                          paddingRight: 48,
                          paddingVertical: 0,
                          textAlignVertical: 'center',
                          color: '#111827',
                          fontWeight: '500',
                        }}
                      />
                      <InputSlot
                        className="absolute right-0 h-full w-14 justify-center items-center"
                        onPress={() => setShowPassword(!showPassword)}
                      >
                        <InputIcon
                          as={showPassword ? EyeOffIcon : EyeIcon}
                          className="text-gray-400"
                          size="md"
                        />
                      </InputSlot>
                    </Input>
                  </Box>
                  {fieldState.error && (
                    <FormControlError className="mt-2">
                      <FormControlErrorIcon
                        as={AlertCircleIcon}
                        className="text-red-500"
                      />
                      <FormControlErrorText className="text-red-500 font-medium">
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              )}
            />

            {/* Sign In Button */}
            <Button
              className="bg-blue-600 rounded-xl h-14 shadow-sm"
              onPress={form.handleSubmit(handleSignIn)}
              disabled={isLoading || !routerReady}
            >
              {isLoading ? (
                <HStack className="items-center" space="sm">
                  <Spinner size="small" color="white" />
                  <ButtonText className="text-white font-semibold text-base">
                    Signing in...
                  </ButtonText>
                </HStack>
              ) : (
                <ButtonText className="text-white font-semibold text-base">
                  Sign in
                </ButtonText>
              )}
            </Button>
          </VStack>
        </Box>

        {/* Sign Up Link */}
        <Box className="mt-4">
          <HStack className="justify-center items-center">
            <Text className="text-gray-600 font-medium mr-2">
              Don't have an account?
            </Text>
            <TouchableOpacity
              onPress={() => {
                Keyboard.dismiss();
                if (router && routerReady) {
                  router.push('/sign-up');
                }
              }}
              disabled={!routerReady}
            >
              <Text className="text-blue-600 font-semibold">Sign up</Text>
            </TouchableOpacity>
          </HStack>
        </Box>

        {/* Divider */}
        <Box className="flex-row items-center">
          <Box className="flex-1 h-px bg-gray-200" />
          <Text className="mx-4 text-gray-500 font-medium">OR</Text>
          <Box className="flex-1 h-px bg-gray-200" />
        </Box>

        {/* Social Sign In */}
        <SocialSignIn
          onGooglePress={handleGoogleSignIn}
          onApplePress={handleAppleSignIn}
        />
      </VStack>
    </Box>
  );
}
