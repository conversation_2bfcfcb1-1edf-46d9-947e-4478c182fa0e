import * as Location from 'expo-location';
import { Redirect, useRouter, usePathname } from 'expo-router';
import React, { useCallback, useEffect, useState, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
  FlatList,
} from 'react-native';
import {
  PanGestureHandler,
  State,
  ScrollView as GestureScrollView,
} from 'react-native-gesture-handler';
import {
  Apple,
  Dog,
  Car,
  Hammer,
  ShoppingBag,
  Home,
  Leaf,
  Laptop,
  Zap,
  MapPinIcon,
  Heart,
  Baby,
  GraduationCap,
  Briefcase,
  Shirt,
  Music,
  Camera,
  Paintbrush,
  Users,
  Globe,
} from 'lucide-react-native';

import { useAuth, useNeedStore, useSecureEnv } from '@/shared/model';
import { getCategories } from '@/pages/needs';
import { useQuery } from '@tanstack/react-query';
import {
  Button,
  ButtonText,
  MapView,
  <PERSON><PERSON>,
  <PERSON>,
  Heading,
  VStack,
  HStack,
  <PERSON><PERSON>,
  I<PERSON>,
} from '@/shared/ui';
import { Avatar } from '@/shared/ui/gluestack/avatar';

export default function Index() {
  const router = useRouter();
  const { needs, fetchNeeds, isLoading: isLoadingNeeds } = useNeedStore();
  const [locationError, setLocationError] = useState<string | null>(null);

  const { value: googleMapsApiKey, isLoadingSecureEnv } = useSecureEnv(
    'EXPO_PUBLIC_ENCRYPTED_GOOGLE_MAPS_API_KEY'
  );

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['categories'],
    queryFn: getCategories,
  });

  // Function to get category name from ID
  const getCategoryName = (categoryId: string | undefined) => {
    if (!categoryId || !categories) return 'default';
    const category = categories.find((cat) => cat.id === categoryId);
    return category?.name.toLowerCase() || 'default';
  };

  const [region, setRegion] = useState({
    latitude: 49.03985836191832,
    longitude: 2.0781613024585637,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  // Bottom Sheet Animation - Responsive dimensions
  const screenHeight = Dimensions.get('window').height;
  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenHeight < 700;
  const tabBarHeight = Platform.OS === 'ios' ? 88 : 80;
  const bottomSheetMinHeight = isSmallScreen ? 100 : 120;
  const bottomSheetMaxHeight = screenHeight * (isSmallScreen ? 0.65 : 0.7);

  const bottomSheetAnimation = useRef(
    new Animated.Value(bottomSheetMinHeight)
  ).current;
  const [bottomSheetState, setBottomSheetState] = useState<
    'collapsed' | 'expanded'
  >('collapsed');
  const [isGestureActive, setIsGestureActive] = useState(false);

  // Bottom Sheet Functions
  const expandBottomSheet = () => {
    setBottomSheetState('expanded');
    Animated.spring(bottomSheetAnimation, {
      toValue: bottomSheetMaxHeight,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  };

  const collapseBottomSheet = () => {
    setBottomSheetState('collapsed');
    Animated.spring(bottomSheetAnimation, {
      toValue: bottomSheetMinHeight,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: bottomSheetAnimation } }],
    { useNativeDriver: false }
  );

  const onHandlerStateChange = (event: any) => {
    const { state, translationY, velocityY } = event.nativeEvent;

    if (state === State.BEGAN) {
      setIsGestureActive(true);
    }

    if (state === State.END || state === State.CANCELLED) {
      setIsGestureActive(false);

      const threshold = bottomSheetMaxHeight * 0.3;
      const velocityThreshold = Platform.OS === 'ios' ? 500 : 300;

      // Logique améliorée pour tous les devices
      if (velocityY < -velocityThreshold) {
        // Swipe rapide vers le haut
        expandBottomSheet();
      } else if (velocityY > velocityThreshold) {
        // Swipe rapide vers le bas
        collapseBottomSheet();
      } else if (Math.abs(translationY) > threshold) {
        // Mouvement lent mais suffisant
        if (translationY < 0) {
          expandBottomSheet();
        } else {
          collapseBottomSheet();
        }
      } else {
        // Retour à l'état actuel
        if (bottomSheetState === 'collapsed') {
          collapseBottomSheet();
        } else {
          expandBottomSheet();
        }
      }
    }
  };

  const getLocation = useCallback(async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const locationPromise = Location.getCurrentPositionAsync({});
        const timeoutPromise = new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Location timeout')), 5000)
        );

        const location: Location.LocationObject = await Promise.race([
          locationPromise,
          timeoutPromise,
        ]).catch((err) => {
          console.log('Location timeout, using default location');
          return {
            coords: {
              latitude: 49.03985836191832,
              longitude: 2.0781613024585637,
            },
          } as Location.LocationObject;
        });

        setRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });
        setLocationError(null);
      } else {
        console.log('Location permission was denied');
        setLocationError('Location permission denied');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      setLocationError('Failed to get location');
    }
  }, []);

  // Initial data loading
  useEffect(() => {
    fetchNeeds();
    getLocation();
  }, [fetchNeeds, getLocation]);

  // Get current pathname to detect screen focus
  const pathname = usePathname();

  // Refresh data when returning to this screen
  useEffect(() => {
    if (pathname === '/') {
      console.log('Map screen focused - refreshing data');
      fetchNeeds();
      getLocation();
    }
  }, [pathname, fetchNeeds, getLocation]);

  const { session } = useAuth();

  if (!session) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  if (isLoadingSecureEnv || isLoadingNeeds) {
    return (
      <Box className="flex-1 bg-gray-50">
        <Box className="flex-1 justify-center items-center">
          <Spinner size="large" className="text-blue-600" />
          <Text className="mt-4 text-gray-600 font-medium">
            Finding needs near you...
          </Text>
        </Box>
      </Box>
    );
  }

  const getCategoryStyle = (category: string) => {
    const getCategoryIcon = (categoryName: string) => {
      switch (categoryName.toLowerCase()) {
        case 'food':
        case 'alimentaire':
          return <Apple size={16} color="#FFFFFF" />;
        case 'animals':
        case 'animaux':
        case 'pets':
          return <Dog size={16} color="#FFFFFF" />;
        case 'transport':
        case 'transportation':
          return <Car size={16} color="#FFFFFF" />;
        case 'diy':
        case 'bricolage':
        case 'repairs':
        case 'réparations':
          return <Hammer size={16} color="#FFFFFF" />;
        case 'shopping':
        case 'courses':
        case 'groceries':
          return <ShoppingBag size={16} color="#FFFFFF" />;
        case 'cleaning':
        case 'ménage':
        case 'housework':
          return <Home size={16} color="#FFFFFF" />;
        case 'gardening':
        case 'jardinage':
        case 'garden':
          return <Leaf size={16} color="#FFFFFF" />;
        case 'technology':
        case 'gaming':
        case 'technologie':
        case 'tech':
          return <Laptop size={16} color="#FFFFFF" />;
        case 'health':
        case 'santé':
        case 'medical':
          return <Heart size={16} color="#FFFFFF" />;
        case 'childcare':
        case "garde d'enfants":
        case 'babysitting':
          return <Baby size={16} color="#FFFFFF" />;
        case 'education':
        case 'éducation':
        case 'learning':
        case 'tutoring':
          return <GraduationCap size={16} color="#FFFFFF" />;
        case 'work':
        case 'travail':
        case 'professional':
          return <Briefcase size={16} color="#FFFFFF" />;
        case 'clothing':
        case 'vêtements':
        case 'fashion':
          return <Shirt size={16} color="#FFFFFF" />;
        case 'music':
        case 'musique':
        case 'instruments':
          return <Music size={16} color="#FFFFFF" />;
        case 'photography':
        case 'photographie':
        case 'photo':
          return <Camera size={16} color="#FFFFFF" />;
        case 'art':
        case 'arts':
        case 'creative':
          return <Paintbrush size={16} color="#FFFFFF" />;
        case 'community':
        case 'communauté':
        case 'social':
          return <Users size={16} color="#FFFFFF" />;
        case 'events':
        case 'événements':
        case 'party':
          return <Globe size={16} color="#FFFFFF" />;
        default:
          return <Zap size={16} color="#FFFFFF" />;
      }
    };

    const getCategoryColoredIcon = (categoryName: string) => {
      switch (categoryName.toLowerCase()) {
        case 'food':
        case 'alimentaire':
          return <Apple size={18} color="#10B981" />;
        case 'animals':
        case 'animaux':
        case 'pets':
          return <Dog size={18} color="#8B5CF6" />;
        case 'transport':
        case 'transportation':
          return <Car size={18} color="#3B82F6" />;
        case 'diy':
        case 'bricolage':
        case 'repairs':
        case 'réparations':
          return <Hammer size={18} color="#F59E0B" />;
        case 'shopping':
        case 'courses':
        case 'groceries':
          return <ShoppingBag size={18} color="#EF4444" />;
        case 'cleaning':
        case 'ménage':
        case 'housework':
          return <Home size={18} color="#06B6D4" />;
        case 'gardening':
        case 'jardinage':
        case 'garden':
          return <Leaf size={18} color="#84CC16" />;
        case 'technology':
        case 'gaming':
        case 'technologie':
        case 'tech':
          return <Laptop size={18} color="#6366F1" />;
        case 'health':
        case 'santé':
        case 'medical':
          return <Heart size={18} color="#EF4444" />;
        case 'childcare':
        case "garde d'enfants":
        case 'babysitting':
          return <Baby size={18} color="#F59E0B" />;
        case 'education':
        case 'éducation':
        case 'learning':
        case 'tutoring':
          return <GraduationCap size={18} color="#8B5CF6" />;
        case 'work':
        case 'travail':
        case 'professional':
          return <Briefcase size={18} color="#1F2937" />;
        case 'clothing':
        case 'vêtements':
        case 'fashion':
          return <Shirt size={18} color="#EC4899" />;
        case 'music':
        case 'musique':
        case 'instruments':
          return <Music size={18} color="#7C3AED" />;
        case 'photography':
        case 'photographie':
        case 'photo':
          return <Camera size={18} color="#059669" />;
        case 'art':
        case 'arts':
        case 'creative':
          return <Paintbrush size={18} color="#DC2626" />;
        case 'community':
        case 'communauté':
        case 'social':
          return <Users size={18} color="#0891B2" />;
        case 'events':
        case 'événements':
        case 'party':
          return <Globe size={18} color="#7C2D12" />;
        default:
          return <Zap size={18} color="#6B7280" />;
      }
    };

    switch (category) {
      case 'food':
      case 'alimentaire':
        return {
          color: '#fff',
          backgroundColor: '#10B981',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#10B981', '#059669'],
        };
      case 'animals':
      case 'animaux':
      case 'pets':
        return {
          color: '#fff',
          backgroundColor: '#8B5CF6',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#8B5CF6', '#7C3AED'],
        };
      case 'transport':
      case 'transportation':
        return {
          color: '#fff',
          backgroundColor: '#3B82F6',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#3B82F6', '#2563EB'],
        };
      case 'diy':
      case 'bricolage':
      case 'repairs':
      case 'réparations':
        return {
          color: '#fff',
          backgroundColor: '#F59E0B',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#F59E0B', '#D97706'],
        };
      case 'shopping':
      case 'courses':
      case 'groceries':
        return {
          color: '#fff',
          backgroundColor: '#EF4444',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#EF4444', '#DC2626'],
        };
      case 'cleaning':
      case 'ménage':
      case 'housework':
        return {
          color: '#fff',
          backgroundColor: '#06B6D4',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#06B6D4', '#0891B2'],
        };
      case 'gardening':
      case 'jardinage':
      case 'garden':
        return {
          color: '#fff',
          backgroundColor: '#84CC16',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#84CC16', '#65A30D'],
        };
      case 'technology':
      case 'gaming':
      case 'technologie':
      case 'tech':
        return {
          color: '#fff',
          backgroundColor: '#6366F1',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#6366F1', '#4F46E5'],
        };
      case 'health':
      case 'santé':
      case 'medical':
        return {
          color: '#fff',
          backgroundColor: '#EF4444',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#EF4444', '#DC2626'],
        };
      case 'childcare':
      case "garde d'enfants":
      case 'babysitting':
        return {
          color: '#fff',
          backgroundColor: '#F59E0B',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#F59E0B', '#D97706'],
        };
      case 'education':
      case 'éducation':
      case 'learning':
      case 'tutoring':
        return {
          color: '#fff',
          backgroundColor: '#8B5CF6',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#8B5CF6', '#7C3AED'],
        };
      case 'work':
      case 'travail':
      case 'professional':
        return {
          color: '#fff',
          backgroundColor: '#1F2937',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#1F2937', '#111827'],
        };
      case 'clothing':
      case 'vêtements':
      case 'fashion':
        return {
          color: '#fff',
          backgroundColor: '#EC4899',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#EC4899', '#DB2777'],
        };
      case 'music':
      case 'musique':
      case 'instruments':
        return {
          color: '#fff',
          backgroundColor: '#7C3AED',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#7C3AED', '#6D28D9'],
        };
      case 'photography':
      case 'photographie':
      case 'photo':
        return {
          color: '#fff',
          backgroundColor: '#059669',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#059669', '#047857'],
        };
      case 'art':
      case 'arts':
      case 'creative':
        return {
          color: '#fff',
          backgroundColor: '#DC2626',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#DC2626', '#B91C1C'],
        };
      case 'community':
      case 'communauté':
      case 'social':
        return {
          color: '#fff',
          backgroundColor: '#0891B2',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#0891B2', '#0E7490'],
        };
      case 'events':
      case 'événements':
      case 'party':
        return {
          color: '#fff',
          backgroundColor: '#7C2D12',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#7C2D12', '#92400E'],
        };
      default:
        return {
          color: '#fff',
          backgroundColor: '#6B7280',
          icon: getCategoryIcon(category),
          coloredIcon: getCategoryColoredIcon(category),
          gradientColors: ['#6B7280', '#4B5563'],
        };
    }
  };

  // Standard map style
  const standardMapStyle = [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
  ];

  const needMarkers = needs.map((need) => {
    const { icon, backgroundColor, color } = getCategoryStyle(
      getCategoryName(need.categoryId)
    );

    return (
      <Marker
        key={need.id}
        coordinate={{
          latitude: need.location.x,
          longitude: need.location.y,
        }}
        onPress={() => router.push(`/needs/${need.id}`)}
      >
        <View style={styles.modernMarkerContainer}>
          <View style={[styles.modernMarker, { backgroundColor }]}>
            <View style={styles.modernMarkerIcon}>{icon}</View>
          </View>
          <View
            style={[
              styles.modernMarkerTail,
              { borderTopColor: backgroundColor },
            ]}
          />
        </View>
      </Marker>
    );
  });

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Content */}
      <Box className="flex-1 relative">
        <MapView
          key={`map-${needs.length}-${Date.now()}`}
          style={styles.map}
          initialRegion={region}
          provider="google"
          mapType="standard"
          customMapStyle={standardMapStyle}
          // @ts-expect-error - Google Maps API key type issue
          googleMapsApiKey={googleMapsApiKey}
          showsUserLocation={true}
          showsMyLocationButton={false}
          showsCompass={false}
          showsScale={false}
          onMapReady={() => console.log('Map is ready')}
          onRegionChangeComplete={(newRegion) =>
            console.log('Region changed', newRegion)
          }
        >
          {needMarkers}
        </MapView>

        {/* Map Controls Overlay */}
        <Box className="absolute top-20 right-6">
          {/* Current Location Button */}
          <TouchableOpacity
            onPress={getLocation}
            className="w-12 h-12 bg-white rounded-xl shadow-lg items-center justify-center"
          >
            <MapPinIcon size={20} color="#3B82F6" />
          </TouchableOpacity>
        </Box>

        {/* Location Error Overlay */}
        {locationError && (
          <Box className="absolute top-16 left-4 right-16">
            <Box className="bg-amber-50/95 backdrop-blur border border-amber-200 rounded-xl p-3">
              <Text className="text-amber-800 text-sm font-medium text-center">
                📍 {locationError}
              </Text>
            </Box>
          </Box>
        )}
      </Box>

      {/* Bottom Sheet */}
      <Animated.View
        style={[
          styles.bottomSheet,
          {
            height: bottomSheetAnimation,
            bottom: 0,
          },
        ]}
      >
        {/* Handle et Header - Approche différente pour Android */}
        {Platform.OS === 'ios' ? (
          // iOS - Avec PanGestureHandler
          <PanGestureHandler
            onGestureEvent={onGestureEvent}
            onHandlerStateChange={onHandlerStateChange}
            activeOffsetY={[-10, 10]}
            failOffsetX={[-20, 20]}
            shouldCancelWhenOutside={false}
            minPointers={1}
            maxPointers={1}
          >
            <Animated.View style={styles.gestureArea}>
              <TouchableOpacity
                onPress={
                  bottomSheetState === 'collapsed'
                    ? expandBottomSheet
                    : collapseBottomSheet
                }
                style={styles.handleContainer}
                activeOpacity={0.7}
              >
                <View style={styles.handle} />
              </TouchableOpacity>

              <View style={styles.bottomSheetHeader}>
                <Text style={styles.bottomSheetTitle}>
                  Help Requests ({needs.length})
                </Text>
                <TouchableOpacity
                  onPress={
                    bottomSheetState === 'collapsed'
                      ? expandBottomSheet
                      : collapseBottomSheet
                  }
                  style={styles.expandButton}
                  activeOpacity={0.7}
                >
                  <Text style={styles.expandButtonText}>
                    {bottomSheetState === 'collapsed' ? '↑' : '↓'}
                  </Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </PanGestureHandler>
        ) : (
          // Android - Sans PanGestureHandler
          <View style={styles.gestureArea}>
            <TouchableOpacity
              onPress={
                bottomSheetState === 'collapsed'
                  ? expandBottomSheet
                  : collapseBottomSheet
              }
              style={styles.handleContainer}
              activeOpacity={0.7}
            >
              <View style={styles.handle} />
            </TouchableOpacity>

            <View style={styles.bottomSheetHeader}>
              <Text style={styles.bottomSheetTitle}>
                Help Requests ({needs.length})
              </Text>
              <TouchableOpacity
                onPress={
                  bottomSheetState === 'collapsed'
                    ? expandBottomSheet
                    : collapseBottomSheet
                }
                style={styles.expandButton}
                activeOpacity={0.7}
              >
                <Text style={styles.expandButtonText}>
                  {bottomSheetState === 'collapsed' ? '↑' : '↓'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Scrollable Content Area avec FlatList */}
        <View style={styles.scrollContainer}>
          <FlatList
            data={needs}
            keyExtractor={(item) => item.id}
            renderItem={({ item: need, index }) => {
              const { icon, backgroundColor, gradientColors, coloredIcon } =
                getCategoryStyle(getCategoryName(need.categoryId));

              return (
                <TouchableOpacity
                  onPress={() => router.push(`/needs/${need.id}`)}
                  style={[
                    styles.needCard,
                    index === needs.length - 1 && { marginBottom: 20 },
                  ]}
                  activeOpacity={0.7}
                >
                  {/* Background gradient with clip path effect */}
                  <View
                    style={[styles.needCardBackground, { backgroundColor }]}
                  >
                    <View style={styles.needCardAccent} />
                  </View>

                  <View style={styles.needCardContent}>
                    <View style={styles.needHeader}>
                      <View
                        style={[
                          styles.needIconContainer,
                          { backgroundColor: `${backgroundColor}20` },
                        ]}
                      >
                        <View style={styles.needIconWrapper}>
                          {coloredIcon}
                        </View>
                      </View>
                      <View style={styles.needPriority}>
                        <View
                          style={[styles.priorityDot, { backgroundColor }]}
                        />
                      </View>
                    </View>

                    <View style={styles.needContentBody}>
                      <Text style={styles.needTitle} numberOfLines={1}>
                        {need.title}
                      </Text>
                      <Text style={styles.needDescription} numberOfLines={2}>
                        {need.description}
                      </Text>

                      <View style={styles.needFooter}>
                        <Text style={styles.needMeta}>Paris • 2h ago</Text>
                        <View style={styles.needAction}>
                          <Text
                            style={[
                              styles.needActionText,
                              { color: backgroundColor },
                            ]}
                          >
                            Help →
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            }}
            ListEmptyComponent={() => (
              <View
                style={[
                  styles.emptyState,
                  { minHeight: bottomSheetMaxHeight * 0.4 },
                ]}
              >
                <Text style={styles.emptyStateText}>No needs nearby</Text>
                <Text style={styles.emptyStateSubtext}>
                  Be the first to share a need in your community
                </Text>
              </View>
            )}
            style={styles.needsList}
            contentContainerStyle={styles.needsListContent}
            showsVerticalScrollIndicator={false}
            bounces={Platform.OS === 'ios'}
            overScrollMode={Platform.OS === 'android' ? 'never' : 'always'}
            scrollEventThrottle={16}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="handled"
            removeClippedSubviews={Platform.OS === 'android'}
            initialNumToRender={10}
            maxToRenderPerBatch={5}
            windowSize={10}
            updateCellsBatchingPeriod={50}
            getItemLayout={(data, index) => ({
              length: 120, // Hauteur approximative d'une carte
              offset: 120 * index + 12 * index, // Hauteur + margin
              index,
            })}
            ItemSeparatorComponent={null}
            contentInsetAdjustmentBehavior={
              Platform.OS === 'ios' ? 'automatic' : 'never'
            }
            // Propriétés spécifiques pour améliorer les performances
            legacyImplementation={false}
            disableVirtualization={false}
          />
        </View>
      </Animated.View>
    </Box>
  );
}

const styles = StyleSheet.create({
  map: {
    width: '100%',
    height: '100%',
  },

  markerWrapper: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 3,
    borderColor: '#fff',
  },

  markerIcon: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  modernMarkerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  modernMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 8,
    borderWidth: 2,
    borderColor: '#fff',
  },

  modernMarkerIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  modernMarkerTail: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    marginTop: -2,
  },

  // Bottom Sheet Styles - Améliorés pour responsive
  bottomSheet: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: Platform.OS === 'ios' ? 0.1 : 0.15,
    shadowRadius: 12,
    elevation: Platform.OS === 'android' ? 20 : 0,
  },

  gestureArea: {
    backgroundColor: 'transparent',
    maxHeight: 80,
    zIndex: 1,
  },

  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    zIndex: 2,
  },

  handle: {
    width: 40,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },

  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 8,
    paddingTop: 4,
    zIndex: 2,
  },

  bottomSheetTitle: {
    fontSize: Platform.OS === 'ios' ? 20 : 18,
    fontWeight: '700',
    color: '#111827',
  },

  expandButton: {
    padding: 8,
    borderRadius: 8,
    zIndex: 3,
  },

  expandButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3B82F6',
  },

  scrollContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },

  needsList: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },

  needsListContent: {
    paddingTop: 8,
    paddingBottom: Platform.OS === 'android' ? 80 : 20,
    flexGrow: 1,
  },

  needCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: Platform.OS === 'ios' ? 0.08 : 0.12,
    shadowRadius: 8,
    elevation: Platform.OS === 'android' ? 4 : 0,
    borderWidth: Platform.OS === 'android' ? 1 : 0,
    borderColor: '#F1F5F9',
    position: 'relative',
    overflow: 'hidden',
  },

  needCardContent: {
    padding: 16,
    position: 'relative',
    zIndex: 1,
  },

  needIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  needIconText: {
    fontSize: 18,
    color: '#FFFFFF',
  },

  needContent: {
    flex: 1,
  },

  needTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },

  needDescription: {
    fontSize: 13,
    color: '#6B7280',
    lineHeight: 18,
    marginBottom: 6,
  },

  needMeta: {
    fontSize: 11,
    color: '#9CA3AF',
    fontWeight: '500',
  },

  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },

  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 8,
    textAlign: 'center',
  },

  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },

  needCardBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 12,
    opacity: 0.05,
  },

  needCardAccent: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 4,
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },

  needHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },

  needIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },

  needPriority: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },

  priorityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },

  needContentBody: {
    flex: 1,
  },

  needFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },

  needAction: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },

  needActionText: {
    fontSize: 12,
    fontWeight: '600',
  },

  needIconWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
