import { Hono } from 'hono';
import { deleteCookie } from 'hono/cookie';

import { requireAuthMiddleware } from '@/shared/middlewares';
import type { AuthVariables } from '@/shared/model';

import { sessionTokenCookieName } from './auth.config';
import { credentialsAuthRoutes } from './routes/credentials-auth.routes';
import { otpRoutes } from './routes/otp.routes';
import { socialAuthRoutes } from './routes/social-auth.routes';
import { sessionService } from './services/session.service';

export const authRoutes = new Hono<{
  Variables: AuthVariables;
}>();

authRoutes.route('/', credentialsAuthRoutes);

authRoutes.route('/otp', otpRoutes);

authRoutes.route('/', socialAuthRoutes);

authRoutes.get('/session', async (c) => {
  const session = c.get('session');

  return c.json({ session });
});

authRoutes.use(requireAuthMiddleware);

authRoutes.post('/sign-out', async (c) => {
  const session = c.get('session')!;

  await sessionService.deleteSessionById(session.id);
  c.set('session', null);

  deleteCookie(c, sessionTokenCookieName);
  return c.json({
    message: 'Logout successful',
  });
});
