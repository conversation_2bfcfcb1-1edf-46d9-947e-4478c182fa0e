import { Redirect, useRouter } from 'expo-router';
import { Text, View, ScrollView, TouchableOpacity } from 'react-native';

import {
  Clock8,
  <PERSON>Helping,
  MessageCircle,
  Star,
  Settings,
  Shield,
} from 'lucide-react-native';

import { useAuth } from '@/shared/model';
import { useSocket } from '@/app/_providers/socket-provider';
import {
  Avatar,
  AvatarBadge,
  AvatarFallbackText,
  AvatarImage,
  Button,
  ButtonText,
  Divider,
  Heading,
  HStack,
  VStack,
  Box,
  Icon,
  Spinner,
} from '@/shared/ui';
import { useState } from 'react';

export default function Index() {
  const { session, isLoading: isLoadingSession } = useAuth();
  const { socket } = useSocket();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('review');

  if (!session && !isLoadingSession) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pt-20 pb-6">
        <Box className="px-6">
          <Box className="flex-row justify-end items-center mb-6">
            <TouchableOpacity
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
              onPress={() => router.push('/(main)/settings')}
            >
              <Settings size={20} color="#6B7280" />
            </TouchableOpacity>
          </Box>

          {/* User Info Card */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <HStack space="lg" className="items-center">
              <Avatar size="xl">
                <AvatarFallbackText>
                  {`${session?.user.firstName} ${session?.user.lastName}`}
                </AvatarFallbackText>
                <AvatarImage
                  source={{
                    uri: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
                  }}
                />
              </Avatar>

              <VStack className="flex-1" space="xs">
                <Heading className="text-xl font-bold text-gray-900">
                  {`${session?.user.firstName} ${session?.user.lastName}`}
                </Heading>
                <Text className="text-gray-500 font-medium">Paris, France</Text>
                <HStack space="xs" className="items-center">
                  <Star size={16} fill="#3B82F6" color="#3B82F6" />
                  <Text className="text-gray-900">
                    <Text className="font-bold">4.8</Text>
                    <Text className="text-gray-500"> (24 reviews)</Text>
                  </Text>
                </HStack>
              </VStack>
            </HStack>
          </Box>
        </Box>
      </Box>

      <ScrollView className="flex-1 px-6">
        {/* Stats Section */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Your Impact
          </Text>
          <HStack className="justify-between">
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-blue-600">80</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Needs Fulfilled
              </Text>
            </VStack>
            <Box className="w-px bg-gray-200 mx-4 self-stretch" />
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-green-600">3 min</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Avg Response
              </Text>
            </VStack>
            <Box className="w-px bg-gray-200 mx-4 self-stretch" />
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-purple-600">2024</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Member Since
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* Badges Section */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Community Badges
          </Text>
          <HStack className="justify-between">
            <VStack className="items-center flex-1" space="sm">
              <Box className="w-12 h-12 bg-blue-100 rounded-xl items-center justify-center">
                <HandHelping size={24} color="#3B82F6" strokeWidth={1.5} />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                Super Helper
              </Text>
            </VStack>

            <VStack className="items-center flex-1" space="sm">
              <Box className="w-12 h-12 bg-green-100 rounded-xl items-center justify-center">
                <Clock8 size={24} color="#10B981" strokeWidth={1.5} />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                Punctual
              </Text>
            </VStack>

            <VStack className="items-center flex-1" space="sm">
              <Box className="w-12 h-12 bg-purple-100 rounded-xl items-center justify-center">
                <MessageCircle size={24} color="#8B5CF6" strokeWidth={1.5} />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                Communicative
              </Text>
            </VStack>

            <VStack className="items-center flex-1" space="sm">
              <Box className="w-12 h-12 bg-yellow-100 rounded-xl items-center justify-center">
                <Star
                  size={24}
                  color="#F59E0B"
                  strokeWidth={1.5}
                  fill="#F59E0B"
                />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                5 Stars
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* Premium Section */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <VStack space="lg">
            <HStack className="items-center" space="sm">
              <Box className="w-12 h-12 bg-blue-100 rounded-xl items-center justify-center">
                <Shield size={24} color="#3B82F6" />
              </Box>
              <VStack className="flex-1" space="xs">
                <Text className="text-gray-900 font-bold text-xl">
                  NeedIt Premium
                </Text>
                <Text className="text-gray-600 text-sm leading-relaxed">
                  Unlock priority support, exclusive features, and enhanced
                  visibility for your needs
                </Text>
              </VStack>
            </HStack>

            <TouchableOpacity
              onPress={() => router.push('/(main)/subscribe')}
              className="bg-blue-600 py-4 rounded-xl shadow-sm"
              style={{
                backgroundColor: '#3B82F6',
                shadowColor: '#3B82F6',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 4,
                elevation: 4,
              }}
            >
              <Text className="text-white font-bold text-center text-base">
                Upgrade to Premium
              </Text>
            </TouchableOpacity>
          </VStack>
        </Box>

        {/* Activity Tabs */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
          <HStack className="border-b border-gray-100">
            <TouchableOpacity
              onPress={() => setActiveTab('review')}
              className={`flex-1 py-4 px-6 ${
                activeTab === 'review'
                  ? 'border-b-2 border-blue-600 bg-blue-50'
                  : 'bg-white'
              }`}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === 'review' ? 'text-blue-600' : 'text-gray-600'
                }`}
              >
                Reviews
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setActiveTab('history')}
              className={`flex-1 py-4 px-6 ${
                activeTab === 'history'
                  ? 'border-b-2 border-blue-600 bg-blue-50'
                  : 'bg-white'
              }`}
            >
              <Text
                className={`text-center font-semibold ${
                  activeTab === 'history' ? 'text-blue-600' : 'text-gray-600'
                }`}
              >
                History
              </Text>
            </TouchableOpacity>
          </HStack>

          {/* Tab Content */}
          <Box className="p-6">
            {activeTab === 'review' ? (
              <VStack space="md">
                {/* Sample Review */}
                <HStack space="sm" className="items-start">
                  <Avatar size="sm">
                    <AvatarFallbackText>JD</AvatarFallbackText>
                  </Avatar>
                  <VStack className="flex-1" space="xs">
                    <HStack className="justify-between items-center">
                      <Text className="font-semibold text-gray-900">
                        John Doe
                      </Text>
                      <HStack space="xs">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            size={12}
                            fill="#3B82F6"
                            color="#3B82F6"
                          />
                        ))}
                      </HStack>
                    </HStack>
                    <Text className="text-gray-600 text-sm">
                      "Very helpful and quick response! Got exactly what I
                      needed."
                    </Text>
                    <Text className="text-gray-400 text-xs">2 days ago</Text>
                  </VStack>
                </HStack>
                <Box className="border-b border-gray-100" />
                {/* Add more reviews here */}
              </VStack>
            ) : (
              <VStack space="md">
                {/* Sample History Item */}
                <HStack space="sm" className="items-center">
                  <Box className="w-8 h-8 bg-green-100 rounded-lg items-center justify-center">
                    <Text className="text-green-600 text-xs">✓</Text>
                  </Box>
                  <VStack className="flex-1" space="xs">
                    <Text className="font-semibold text-gray-900">
                      Helped with grocery shopping
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      Completed • €15 earned
                    </Text>
                  </VStack>
                  <Text className="text-gray-400 text-xs">1 week ago</Text>
                </HStack>
                <Box className="border-b border-gray-100" />
                {/* Add more history items here */}
              </VStack>
            )}
          </Box>
        </Box>
      </ScrollView>
    </Box>
  );
}
