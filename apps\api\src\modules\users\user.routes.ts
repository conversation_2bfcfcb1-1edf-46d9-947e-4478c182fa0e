import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';

import status from 'http-status';

import {
  credentialsAccountSelectDto,
  credentialsAccountService,
} from '@/modules/auth';

import { applyDtoSchema } from '@/shared/lib';

import { userSelectDto } from './user.dto';
import { userService } from './user.service';
import { profileRoutes } from './profile.routes';

export const userRoutes = new Hono();

// Mount profile routes
userRoutes.route('/profile', profileRoutes);

userRoutes.get(
  '/',
  zValidator('query', userSelectDto.pick({ email: true })),
  async (c) => {
    const query = c.req.valid('query');

    const user = await userService.getUserByEmail(query!.email);

    if (!user) {
      throw new HTTPException(status.NOT_FOUND, {
        res: Response.json({
          error: {
            code: status[`${status.NOT_FOUND}_NAME`],
            message: `User not found for email ${query!.email}`,
          },
        }),
      });
    }

    return c.json({ user });
  }
);

userRoutes.get(
  '/:id/credentials',
  zValidator('param', userSelectDto.pick({ id: true })),
  async (c) => {
    const params = c.req.valid('param');

    const user = await userService.getUserById(params.id);

    if (!user) {
      throw new HTTPException(status.NOT_FOUND, {
        res: Response.json({
          error: {
            code: status[`${status.NOT_FOUND}_NAME`],
            message: `User not found for id ${params.id}`,
          },
        }),
      });
    }

    const credentials =
      await credentialsAccountService.getUserCredentialsAccount(user.id);

    if (!credentials) {
      throw new HTTPException(status.NOT_FOUND, {
        res: Response.json({
          error: {
            code: status[`${status.NOT_FOUND}_NAME`],
            message: `Credentials not found for user id ${user.id}`,
          },
        }),
      });
    }

    const credentialsDto = applyDtoSchema(
      credentials,
      credentialsAccountSelectDto
    );

    return c.json({ credentials: credentialsDto });
  }
);
