import { SettingsRepository } from './settings.repository';
import type {
  SettingsSelectDto,
  SettingsUpdateDto,
  NotificationSettingsDto,
  PrivacySettingsDto,
  AppearanceSettingsDto,
} from './settings.dto';

export class SettingsService {
  private settingsRepository: SettingsRepository;

  constructor() {
    this.settingsRepository = new SettingsRepository();
  }

  /**
   * Get user settings (creates default if not exists)
   */
  async getUserSettings(userId: string): Promise<SettingsSelectDto> {
    return this.settingsRepository.getOrCreate(userId);
  }

  /**
   * Update user settings
   */
  async updateSettings(
    userId: string,
    data: SettingsUpdateDto
  ): Promise<SettingsSelectDto> {
    // Ensure user settings exist
    await this.settingsRepository.getOrCreate(userId);

    const updatedSettings = await this.settingsRepository.update(userId, data);

    if (!updatedSettings) {
      throw new Error('Failed to update settings');
    }

    return updatedSettings;
  }

  /**
   * Update notification settings specifically
   */
  async updateNotificationSettings(
    userId: string,
    data: NotificationSettingsDto
  ): Promise<SettingsSelectDto> {
    return this.updateSettings(userId, data);
  }

  /**
   * Update privacy settings specifically
   */
  async updatePrivacySettings(
    userId: string,
    data: PrivacySettingsDto
  ): Promise<SettingsSelectDto> {
    return this.updateSettings(userId, data);
  }

  /**
   * Update appearance settings specifically
   */
  async updateAppearanceSettings(
    userId: string,
    data: AppearanceSettingsDto
  ): Promise<SettingsSelectDto> {
    return this.updateSettings(userId, data);
  }

  /**
   * Reset user settings to default
   */
  async resetToDefaults(userId: string): Promise<SettingsSelectDto> {
    const defaultSettings: SettingsUpdateDto = {
      language: 'en',
      darkMode: false,
      notificationsEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      marketingEmails: false,
      profileVisibility: 'public',
      locationSharing: true,
    };

    return this.updateSettings(userId, defaultSettings);
  }

  /**
   * Delete user settings
   */
  async deleteUserSettings(userId: string): Promise<void> {
    await this.settingsRepository.delete(userId);
  }
}
