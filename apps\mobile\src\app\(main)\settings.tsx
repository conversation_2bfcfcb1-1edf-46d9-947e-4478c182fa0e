import { useRouter } from 'expo-router';
import { Text, TouchableOpacity, Switch, ScrollView } from 'react-native';
import { useState, useEffect } from 'react';

import {
  ArrowLeft,
  CreditCard,
  Bell,
  Shield,
  Globe,
  HelpCircle,
  LogOut,
  ChevronRight,
  User,
  Settings as SettingsIcon,
} from 'lucide-react-native';

import { signOut } from '@/shared/api';
import { useAuth } from '@/shared/model';
import { useSocket } from '@/app/_providers/socket-provider';
import {
  useSettings,
  useUpdateNotificationSettings,
  useUpdateAppearanceSettings,
  useUpdatePrivacySettings,
} from '@/shared/model/hooks/use-settings';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Icon,
  Divider,
  SafeAreaView,
} from '@/shared/ui';

export default function Settings() {
  const router = useRouter();
  const { session } = useAuth();
  const { socket } = useSocket();

  // Settings hooks
  const { settings, isLoading: isSettingsLoading } = useSettings();
  const updateNotificationSettings = useUpdateNotificationSettings();
  const updateAppearanceSettings = useUpdateAppearanceSettings();
  const updatePrivacySettings = useUpdatePrivacySettings();

  // Local state for UI - sync with backend data
  const [notifications, setNotifications] = useState(
    settings?.notificationsEnabled ?? true
  );

  // Sync local state with backend data when settings change
  useEffect(() => {
    if (settings) {
      setNotifications(settings.notificationsEnabled);
    }
  }, [settings]);

  // Handlers for settings changes
  const handleNotificationsChange = (value: boolean) => {
    setNotifications(value);
    updateNotificationSettings.mutate({
      notificationsEnabled: value,
      emailNotifications: settings?.emailNotifications ?? true,
      pushNotifications: settings?.pushNotifications ?? true,
      marketingEmails: settings?.marketingEmails ?? false,
    });
  };

  const handleSignOut = async () => {
    // Disconnect socket if connected
    if (socket && socket.connected) {
      socket.disconnect();
      console.log('Socket disconnected during logout');
    }

    // Call the signOut function to handle server-side logout and cache clearing
    await signOut();

    // Navigate to sign-in page
    router.push('/sign-in');
  };

  const SettingItem = ({
    icon: IconComponent,
    title,
    subtitle,
    onPress,
    rightElement,
    iconColor = '#6B7280',
    iconBgColor = '#F3F4F6',
    showChevron = true,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
    iconColor?: string;
    iconBgColor?: string;
    showChevron?: boolean;
  }) => (
    <Box className="w-full">
      <TouchableOpacity onPress={onPress} disabled={!onPress}>
        <HStack space="md" className="items-center py-4 px-0">
          <Box
            className="w-10 h-10 rounded-xl items-center justify-center"
            style={{ backgroundColor: iconBgColor }}
          >
            <IconComponent size={20} color={iconColor} />
          </Box>
          <VStack className="flex-1" space="xs">
            <Text
              className="text-gray-900 font-semibold text-base"
              numberOfLines={1}
            >
              {title}
            </Text>
            {subtitle && (
              <Text className="text-gray-500 text-sm" numberOfLines={2}>
                {subtitle}
              </Text>
            )}
          </VStack>
          <Box className="ml-2">
            {rightElement ||
              (showChevron && <ChevronRight size={20} color="#9CA3AF" />)}
          </Box>
        </HStack>
      </TouchableOpacity>
    </Box>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Settings
            </Heading>
          </HStack>
        </Box>
      </Box>

      {/* Settings Content */}
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
        showsVerticalScrollIndicator={false}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Account Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <VStack space="xs" className="p-6">
              <Text className="text-sm font-bold text-gray-700 mb-2 uppercase tracking-wider">
                Account
              </Text>

              <SettingItem
                icon={User}
                title="Profile Information"
                subtitle="Edit your personal details"
                onPress={() => router.push('/(main)/profile-edit')}
                iconColor="#3B82F6"
                iconBgColor="#EFF6FF"
              />

              <Box className="w-full h-px bg-gray-100 my-2" />

              <SettingItem
                icon={CreditCard}
                title="Payment Methods"
                subtitle="Manage your payment options"
                onPress={() => router.push('/(main)/payment-methods')}
                iconColor="#10B981"
                iconBgColor="#F0FDF4"
              />
            </VStack>
          </Box>

          {/* Preferences Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <VStack space="xs" className="p-6">
              <Text className="text-sm font-bold text-gray-700 mb-2 uppercase tracking-wider">
                Preferences
              </Text>

              <SettingItem
                icon={Bell}
                title="Notifications"
                subtitle="Push notifications and alerts"
                onPress={() => router.push('/(main)/notifications')}
                rightElement={
                  <Switch
                    value={notifications}
                    onValueChange={handleNotificationsChange}
                    trackColor={{ false: '#F3F4F6', true: '#3B82F6' }}
                    thumbColor={notifications ? '#FFFFFF' : '#9CA3AF'}
                  />
                }
                showChevron={false}
                iconColor="#F59E0B"
                iconBgColor="#FFFBEB"
              />

              <Box className="w-full h-px bg-gray-100 my-2" />

              <SettingItem
                icon={Globe}
                title="Language"
                subtitle={
                  settings?.language === 'en'
                    ? 'English (EN)'
                    : settings?.language || 'English (EN)'
                }
                onPress={() => router.push('/(main)/language')}
                iconColor="#06B6D4"
                iconBgColor="#F0F9FF"
              />
            </VStack>
          </Box>

          {/* Privacy & Security Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <VStack space="xs" className="p-6">
              <Text className="text-sm font-bold text-gray-700 mb-2 uppercase tracking-wider">
                Privacy & Security
              </Text>

              <SettingItem
                icon={Shield}
                title="Privacy Settings"
                subtitle="Control your data and visibility"
                onPress={() => router.push('/(main)/privacy')}
                iconColor="#10B981"
                iconBgColor="#F0FDF4"
              />
            </VStack>
          </Box>

          {/* Support Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <VStack space="xs" className="p-6">
              <Text className="text-sm font-bold text-gray-700 mb-2 uppercase tracking-wider">
                Support
              </Text>

              <SettingItem
                icon={HelpCircle}
                title="Help & Support"
                subtitle="FAQs and contact support"
                onPress={() => router.push('/(main)/help')}
                iconColor="#6B7280"
                iconBgColor="#F9FAFB"
              />
            </VStack>
          </Box>

          {/* Sign Out Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-red-100 overflow-hidden">
            <TouchableOpacity onPress={handleSignOut} className="p-6">
              <HStack space="md" className="items-center">
                <Box className="w-10 h-10 bg-red-100 rounded-xl items-center justify-center">
                  <LogOut size={20} color="#EF4444" />
                </Box>
                <Text className="text-red-600 font-semibold text-base flex-1">
                  Sign Out
                </Text>
              </HStack>
            </TouchableOpacity>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
