{"expo": {"name": "NeedIt", "jsEngine": "jsc", "slug": "needit", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logoNeedit.webp", "scheme": "needit", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.needit.app"}, "android": {"adaptiveIcon": {"backgroundColor": "#ffffff"}, "package": "com.needit.app", "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logoNeedit.webp"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store", "expo-font", "expo-web-browser"], "experiments": {"typedRoutes": true}, "assetBundlePatterns": ["**/*"]}}