import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, FlatList, Alert } from 'react-native';
import { useRouter } from 'expo-router';

const categories = [
  { id: 'game', label: 'Game', emoji: '🎮' },
  { id: 'food', label: 'Food', emoji: '🍎' },
  { id: 'services', label: 'Services', emoji: '🤝' },
  { id: 'pets', label: 'Pets', emoji: '🐶' },
  { id: 'sos', label: 'SOS', emoji: '🆘' },
  { id: 'mechanics', label: 'Mechanics', emoji: '🛠️' },
];

export default function CategorySelectPage() {
  const [selected, setSelected] = useState<string[]>([]);
  const router = useRouter();

  const toggleCategory = (id: string) => {
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((cat) => cat !== id) : [...prev, id]
    );
  };

 /* export default function CategorySelectPage() {
    const [selected, setSelected] = useState<string[]>([]);
    const [categories, setCategories] = useState<{ id: string, label: string, emoji?: string }[]>([]);
    const router = useRouter();
  
    useEffect(() => {
      fetch('http://<TON_BACKEND>/categories')
        .then((res) => res.json())
        .then((data) => setCategories(data))
        .catch((err) => {
          Alert.alert('Erreur', "Impossible de charger les catégories");
          setCategories([]);
        });
    }, []);
  
    const toggleCategory = (id: string) => {
      setSelected((prev) =>
        prev.includes(id) ? prev.filter((cat) => cat !== id) : [...prev, id]
      );
    };
*/


  const handleSubmit = () => {
    if (selected.length < 2) {
      Alert.alert('Minimum Required', 'Please select at least two categories.');
      return;
    }

    console.log('Selected categories:', selected);

  };

  return (
<View className="flex-1 bg-black px-4 pt-16 justify-between pb-10">
  <View>
    <Text className="text-white text-3xl font-bold mb-2">Welcome to</Text>
    <Text className="text-blue-500 text-3xl font-extrabold mb-6">NeedIt</Text>

    <FlatList
      data={categories}
      keyExtractor={(item) => item.id}
      numColumns={2}
      columnWrapperStyle={{ justifyContent: 'space-between', marginBottom: 16 }}
      renderItem={({ item }) => {
        const isSelected = selected.includes(item.id);
        return (
          <TouchableOpacity
            onPress={() => toggleCategory(item.id)}
            className={`w-[48%] h-28 rounded-xl justify-center items-center ${isSelected ? 'bg-blue-500' : 'bg-white'}`}
          >
            <Text className="text-4xl">{item.emoji}</Text>
            <Text className={`mt-2 text-lg font-semibold ${isSelected ? 'text-white' : 'text-black'}`}>
              {item.label}
            </Text>
          </TouchableOpacity>
        );
      }}
    />
  </View>

  <View>
    <Text className="text-center text-white mt-6 mb-2">TWO MINIMUM</Text>

    <TouchableOpacity
      disabled={selected.length < 2}
      onPress={handleSubmit}
      className={`bg-blue-500 py-3 rounded-lg ${selected.length < 2 ? 'opacity-50' : ''}`}
    >
      <Text className="text-center text-white font-bold text-lg">Sign up</Text>
    </TouchableOpacity>
  </View>
</View>

  );
}
