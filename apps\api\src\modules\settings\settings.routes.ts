import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';

import status from 'http-status';

import { requireAuthMiddleware } from '@/shared/middlewares';
import { applyDtoSchema } from '@/shared/lib';
import type { AuthVariables } from '@/shared/model';

import {
  settingsSelectDto,
  settingsUpdateDto,
  notificationSettingsDto,
  privacySettingsDto,
  appearanceSettingsDto,
} from './settings.dto';
import { settingsService } from './index';

export const settingsRoutes = new Hono<{
  Variables: AuthVariables;
}>();

// Apply auth middleware to all routes
settingsRoutes.use('*', requireAuthMiddleware);

/**
 * GET /api/settings - Get user settings
 */
settingsRoutes.get('/', async (c) => {
  const session = c.get('session');
  if (!session) {
    throw new HTTPException(status.UNAUTHORIZED, {
      res: Response.json({
        error: {
          code: status[`${status.UNAUTHORIZED}_NAME`],
          message: 'Unauthorized',
        },
      }),
    });
  }

  try {
    const settings = await settingsService.getUserSettings(session.userId);
    const settingsDto = applyDtoSchema(settings, settingsSelectDto);

    return c.json({ settings: settingsDto });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message: 'Failed to get user settings',
        },
      }),
    });
  }
});

/**
 * PATCH /api/settings - Update user settings
 */
settingsRoutes.patch('/', zValidator('json', settingsUpdateDto), async (c) => {
  const session = c.get('session');
  if (!session) {
    throw new HTTPException(status.UNAUTHORIZED, {
      res: Response.json({
        error: {
          code: status[`${status.UNAUTHORIZED}_NAME`],
          message: 'Unauthorized',
        },
      }),
    });
  }
  const data = c.req.valid('json');

  try {
    const settings = await settingsService.updateSettings(session.userId, data);
    const settingsDto = applyDtoSchema(settings, settingsSelectDto);

    return c.json({ settings: settingsDto });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message: 'Failed to update settings',
        },
      }),
    });
  }
});

/**
 * PATCH /api/settings/notifications - Update notification settings
 */
settingsRoutes.patch(
  '/notifications',
  zValidator('json', notificationSettingsDto),
  async (c) => {
    const session = c.get('session');
    if (!session) {
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message: 'Unauthorized',
          },
        }),
      });
    }
    const data = c.req.valid('json');

    try {
      const settings = await settingsService.updateNotificationSettings(
        session.userId,
        data
      );
      const settingsDto = applyDtoSchema(settings, settingsSelectDto);

      return c.json({ settings: settingsDto });
    } catch (error) {
      throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
        res: Response.json({
          error: {
            code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
            message: 'Failed to update notification settings',
          },
        }),
      });
    }
  }
);

/**
 * PATCH /api/settings/privacy - Update privacy settings
 */
settingsRoutes.patch(
  '/privacy',
  zValidator('json', privacySettingsDto),
  async (c) => {
    const session = c.get('session');
    if (!session) {
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message: 'Unauthorized',
          },
        }),
      });
    }
    const data = c.req.valid('json');

    try {
      const settings = await settingsService.updatePrivacySettings(
        session.userId,
        data
      );
      const settingsDto = applyDtoSchema(settings, settingsSelectDto);

      return c.json({ settings: settingsDto });
    } catch (error) {
      throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
        res: Response.json({
          error: {
            code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
            message: 'Failed to update privacy settings',
          },
        }),
      });
    }
  }
);

/**
 * PATCH /api/settings/appearance - Update appearance settings
 */
settingsRoutes.patch(
  '/appearance',
  zValidator('json', appearanceSettingsDto),
  async (c) => {
    const session = c.get('session');
    if (!session) {
      throw new HTTPException(status.UNAUTHORIZED, {
        res: Response.json({
          error: {
            code: status[`${status.UNAUTHORIZED}_NAME`],
            message: 'Unauthorized',
          },
        }),
      });
    }
    const data = c.req.valid('json');

    try {
      const settings = await settingsService.updateAppearanceSettings(
        session.userId,
        data
      );
      const settingsDto = applyDtoSchema(settings, settingsSelectDto);

      return c.json({ settings: settingsDto });
    } catch (error) {
      throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
        res: Response.json({
          error: {
            code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
            message: 'Failed to update appearance settings',
          },
        }),
      });
    }
  }
);

/**
 * POST /api/settings/reset - Reset settings to defaults
 */
settingsRoutes.post('/reset', async (c) => {
  const session = c.get('session');
  if (!session) {
    throw new HTTPException(status.UNAUTHORIZED, {
      res: Response.json({
        error: {
          code: status[`${status.UNAUTHORIZED}_NAME`],
          message: 'Unauthorized',
        },
      }),
    });
  }

  try {
    const settings = await settingsService.resetToDefaults(session.userId);
    const settingsDto = applyDtoSchema(settings, settingsSelectDto);

    return c.json({ settings: settingsDto });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message: 'Failed to reset settings',
        },
      }),
    });
  }
});

/**
 * DELETE /api/settings - Delete user settings
 */
settingsRoutes.delete('/', async (c) => {
  const session = c.get('session');
  if (!session) {
    throw new HTTPException(status.UNAUTHORIZED, {
      res: Response.json({
        error: {
          code: status[`${status.UNAUTHORIZED}_NAME`],
          message: 'Unauthorized',
        },
      }),
    });
  }

  try {
    await settingsService.deleteUserSettings(session.userId);

    return c.json({ message: 'Settings deleted successfully' });
  } catch (error) {
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message: 'Failed to delete settings',
        },
      }),
    });
  }
});
