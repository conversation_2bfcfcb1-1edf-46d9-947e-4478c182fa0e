import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import {
  getProfile,
  updateProfile,
  updateProfilePicture,
  getProfileStats,
  getRecentNeeds,
  getRecentOffers,
  getActivityTimeline,
  type UserProfile,
  type ProfileUpdateData,
  type ProfileStats,
} from '@/shared/api';

const PROFILE_QUERY_KEY = ['profile'];
const PROFILE_STATS_QUERY_KEY = ['profile', 'stats'];
const PROFILE_NEEDS_QUERY_KEY = ['profile', 'needs'];
const PROFILE_OFFERS_QUERY_KEY = ['profile', 'offers'];
const PROFILE_TIMELINE_QUERY_KEY = ['profile', 'timeline'];

/**
 * Hook to get user profile
 */
export function useProfile() {
  const {
    data: profile,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: PROFILE_QUERY_KEY,
    queryFn: getProfile,
  });

  return {
    profile,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook to update profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (profileData: ProfileUpdateData) => updateProfile(profileData),
    onSuccess: (data) => {
      queryClient.setQueryData(PROFILE_QUERY_KEY, data);
      console.log('Profile updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update profile:', error.message);
    },
  });
}

/**
 * Hook to update profile picture
 */
export function useUpdateProfilePicture() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (imageRef: string) => updateProfilePicture(imageRef),
    onSuccess: (data) => {
      queryClient.setQueryData(PROFILE_QUERY_KEY, data);
      console.log('Profile picture updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update profile picture:', error.message);
    },
  });
}

/**
 * Hook to get profile stats
 */
export function useProfileStats() {
  const {
    data: stats,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: PROFILE_STATS_QUERY_KEY,
    queryFn: getProfileStats,
  });

  return {
    stats,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook to get recent needs
 */
export function useRecentNeeds() {
  const {
    data: needs,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: PROFILE_NEEDS_QUERY_KEY,
    queryFn: getRecentNeeds,
  });

  return {
    needs,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook to get recent offers
 */
export function useRecentOffers() {
  const {
    data: offers,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: PROFILE_OFFERS_QUERY_KEY,
    queryFn: getRecentOffers,
  });

  return {
    offers,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook to get activity timeline
 */
export function useActivityTimeline() {
  const {
    data: timeline,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: PROFILE_TIMELINE_QUERY_KEY,
    queryFn: getActivityTimeline,
  });

  return {
    timeline,
    error,
    isLoading,
    refetch,
  };
}
