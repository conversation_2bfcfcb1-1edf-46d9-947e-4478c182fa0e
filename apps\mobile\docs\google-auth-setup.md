# Configuration de l'Authentification Google

Ce document explique comment configurer l'authentification Google pour l'application mobile NeedIt.

## Étapes pour configurer l'authentification Google

### 1. C<PERSON>er un projet dans Google Cloud Console

1. Accédez à [Google Cloud Console](https://console.cloud.google.com/)
2. Créez un nouveau projet ou sélectionnez un projet existant
3. Notez l'ID du projet, vous en aurez besoin plus tard

### 2. Configurer l'écran de consentement OAuth

1. Dans le menu de navigation, allez à "APIs & Services" > "OAuth consent screen"
2. Sélectionnez le type d'utilisateur (externe ou interne)
3. Remplissez les informations requises :
   - Nom de l'application
   - Email de contact
   - Logo de l'application (optionnel)
   - Domaines autorisés
4. Ajoutez les scopes nécessaires (au minimum "email" et "profile")
5. Ajoutez les utilisateurs de test si vous êtes en mode externe
6. C<PERSON>z sur "Save and Continue" pour terminer la configuration

### 3. <PERSON><PERSON>er les identifiants OAuth

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. C<PERSON>z sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez le type d'application :

#### Pour le Web (Web Client ID)
1. Sélectionnez "Web application"
2. Donnez un nom à votre client
3. Ajoutez les origines JavaScript autorisées (par exemple, `http://localhost:3000`)
4. Ajoutez les URI de redirection autorisées (par exemple, `http://localhost:3000/auth/google/callback`)
5. Cliquez sur "Create"
6. Notez le Client ID généré

#### Pour Android (Android Client ID)
1. Sélectionnez "Android"
2. Donnez un nom à votre client
3. Ajoutez le nom du package de votre application
4. Ajoutez l'empreinte SHA-1 de votre certificat de signature
5. Cliquez sur "Create"
6. Notez le Client ID généré

#### Pour iOS (iOS Client ID)
1. Sélectionnez "iOS"
2. Donnez un nom à votre client
3. Ajoutez le Bundle ID de votre application
4. Cliquez sur "Create"
5. Notez le Client ID généré

#### Pour Expo (Expo Client ID)
Pour Expo, vous pouvez utiliser le même client ID que pour le Web, mais assurez-vous d'ajouter les URI de redirection spécifiques à Expo.

### 4. Activer l'API Google Sign-In

1. Dans le menu de navigation, allez à "APIs & Services" > "Library"
2. Recherchez "Google Sign-In API" ou "Google Identity Services"
3. Sélectionnez l'API et cliquez sur "Enable"

### 5. Configurer les variables d'environnement

Mettez à jour le fichier `.env` avec les identifiants que vous avez obtenus :

```
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID="votre_web_client_id"
EXPO_PUBLIC_GOOGLE_EXPO_CLIENT_ID="votre_expo_client_id"
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID="votre_ios_client_id"
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID="votre_android_client_id"
```

### 6. Redémarrer l'application

Après avoir mis à jour les variables d'environnement, redémarrez votre application pour que les changements prennent effet.

## Tester l'authentification Google

1. Lancez l'application mobile
2. Accédez à l'écran de connexion
3. Appuyez sur le bouton "Continue with Google"
4. Vous devriez être redirigé vers l'écran de connexion Google
5. Après avoir sélectionné votre compte Google, vous devriez être redirigé vers l'application et être connecté

## Dépannage

Si vous rencontrez des problèmes avec l'authentification Google :

1. Vérifiez que tous les identifiants sont correctement configurés dans le fichier `.env`
2. Assurez-vous que les URI de redirection sont correctement configurées dans la console Google Cloud
3. Vérifiez les journaux de l'application pour les erreurs spécifiques
4. Pour les applications Expo, assurez-vous que vous utilisez la dernière version de `expo-auth-session`

## Ressources supplémentaires

- [Documentation Google OAuth 2.0](https://developers.google.com/identity/protocols/oauth2)
- [Documentation Expo Auth Session](https://docs.expo.dev/versions/latest/sdk/auth-session/)
- [Guide d'authentification Google pour React Native](https://docs.expo.dev/guides/authentication/#google)
