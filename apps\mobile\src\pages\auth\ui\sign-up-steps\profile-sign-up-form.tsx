import { View, TouchableOpacity, Alert } from 'react-native';

import { Controller, UseFormReturn } from 'react-hook-form';

import { type ProfileSchema } from '@/pages/auth';

import {
  AlertCircleIcon,
  Button,
  ButtonText,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
  HStack,
  Image,
  Input,
  InputField,
  Text,
  VStack,
  Icon,
  AddIcon,
  EditIcon,
  Box,
} from '@/shared/ui';

interface Props {
  form: UseFormReturn<ProfileSchema>;
  onPickImage: () => void;
  onTakePhoto: () => void;
}

export function ProfileSignUpForm({ form, onPickImage, onTakePhoto }: Props) {
  const imageUri = form.watch('imageUri');

  const handlePhotoSelection = () => {
    Alert.alert(
      'Select Photo',
      'Choose how you want to add your profile picture',
      [
        {
          text: 'Camera',
          onPress: onTakePhoto,
        },
        {
          text: 'Photo Library',
          onPress: onPickImage,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return (
    <VStack space="lg">
      {/* Profile Photo Section */}
      <VStack space="md" className="items-center">
        <TouchableOpacity onPress={handlePhotoSelection} activeOpacity={0.7}>
          <Box className="relative">
            {imageUri ? (
              <Image
                source={{ uri: imageUri }}
                className="w-32 h-32 rounded-full border-4 border-white shadow-lg"
                alt="Profile image"
              />
            ) : (
              <Box className="w-32 h-32 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 border-4 border-white shadow-lg items-center justify-center">
                <Icon as={AddIcon} size="xl" className="text-blue-600" />
              </Box>
            )}

            {/* Edit indicator */}
            <Box className="absolute -bottom-1 -right-1 w-10 h-10 bg-blue-600 rounded-full shadow-md items-center justify-center border-3 border-white">
              <Icon as={EditIcon} size="md" className="text-white" />
            </Box>
          </Box>
        </TouchableOpacity>

        <VStack space="xs" className="items-center">
          <Text className="text-lg font-semibold text-gray-900">
            Add your photo
          </Text>
          <Text className="text-sm text-gray-500 text-center max-w-xs">
            Upload a profile picture so others can recognize you
          </Text>
        </VStack>
      </VStack>

      {/* Name Fields */}
      <VStack space="lg">
        {/* First Name Field */}
        <Controller
          control={form.control}
          name="firstName"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined}>
              <Text className="text-sm font-semibold text-gray-700 mb-3">
                First name
              </Text>
              <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                <Input className="border-0 bg-transparent h-full">
                  <InputField
                    placeholder="Enter your first name"
                    value={field.value}
                    onChangeText={field.onChange}
                    autoCapitalize="words"
                    autoComplete="given-name"
                    autoCorrect={false}
                    style={{
                      fontSize: 16,
                      lineHeight: 20,
                      height: 56,
                      paddingHorizontal: 16,
                      paddingVertical: 0,
                      textAlignVertical: 'center',
                      color: '#111827',
                      fontWeight: '500',
                    }}
                  />
                </Input>
              </Box>
              {fieldState.error && (
                <FormControlError className="mt-2">
                  <FormControlErrorIcon
                    as={AlertCircleIcon}
                    className="text-red-500"
                  />
                  <FormControlErrorText className="text-red-500 font-medium">
                    {fieldState.error?.message}
                  </FormControlErrorText>
                </FormControlError>
              )}
            </FormControl>
          )}
        />

        {/* Last Name Field */}
        <Controller
          control={form.control}
          name="lastName"
          render={({ field, fieldState }) => (
            <FormControl isInvalid={fieldState.error !== undefined}>
              <Text className="text-sm font-semibold text-gray-700 mb-3">
                Last name
              </Text>
              <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                <Input className="border-0 bg-transparent h-full">
                  <InputField
                    placeholder="Enter your last name"
                    value={field.value}
                    onChangeText={field.onChange}
                    autoCapitalize="words"
                    autoComplete="family-name"
                    autoCorrect={false}
                    style={{
                      fontSize: 16,
                      lineHeight: 20,
                      height: 56,
                      paddingHorizontal: 16,
                      paddingVertical: 0,
                      textAlignVertical: 'center',
                      color: '#111827',
                      fontWeight: '500',
                    }}
                  />
                </Input>
              </Box>
              {fieldState.error && (
                <FormControlError className="mt-2">
                  <FormControlErrorIcon
                    as={AlertCircleIcon}
                    className="text-red-500"
                  />
                  <FormControlErrorText className="text-red-500 font-medium">
                    {fieldState.error?.message}
                  </FormControlErrorText>
                </FormControlError>
              )}
            </FormControl>
          )}
        />
      </VStack>
    </VStack>
  );
}
