import React from 'react';
import { View } from 'react-native';
import { Text, VStack } from '@/shared/ui';
import { RegularMessageProps } from '../types/message-types';

const RegularMessage = React.memo((props: RegularMessageProps) => {
  const { id, text, sender, timestamp } = props;
  const isUser = sender === 'user';

  return (
    <VStack
      className={`mb-4 max-w-[80%] ${isUser ? 'self-end' : 'self-start'}`}
      space="xs"
    >
      <View
        className={`rounded-2xl px-4 py-3 shadow-sm ${
          isUser
            ? 'bg-blue-600 rounded-br-md'
            : 'bg-white rounded-bl-md border border-gray-200'
        }`}
      >
        <Text
          className={`${isUser ? 'text-white' : 'text-gray-900'} font-body`}
          size="md"
        >
          {text}
        </Text>
      </View>
      <Text
        className={`text-gray-500 font-body ${
          isUser ? 'text-right mr-2' : 'text-left ml-2'
        }`}
        size="xs"
      >
        {timestamp}
      </Text>
    </VStack>
  );
});

RegularMessage.displayName = 'RegularMessage';

export default RegularMessage;
