import { createContext, useContext, useEffect, useState } from 'react';

import { io, Socket } from 'socket.io-client';

import type {
  ClientToServerEvents,
  ServerToClientEvents,
} from '@needit/socket';

import { backendBaseUrl } from '@/shared/config';
import { useToast, Toast, ToastTitle, ToastDescription } from '@/shared/ui';

type SocketContextType = {
  socket: Socket | null;
  isConnected: boolean;
};

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

export const useSocket = () => useContext(SocketContext);

interface SocketProviderProps {
  children: React.ReactNode;
}

export default function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket<
    ServerToClientEvents,
    ClientToServerEvents
  > | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const toast = useToast();

  useEffect(() => {
    // Initialisation de la connexion socket
    const socketInstance: Socket<ServerToClientEvents, ClientToServerEvents> =
      io(backendBaseUrl);

    // Gestion des événements de connexion
    socketInstance.on('connect', () => {
      console.log('Socket.io connected');
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('Socket.io disconnected');
      setIsConnected(false);
    });

    // Gestion de l'événement de nouvelle offre - NOTIFICATIONS SUPPRIMÉES
    socketInstance.on(
      'newOffer',
      (data: {
        offerId: string;
        needId: string;
        needTitle: string;
        userId: string;
      }) => {
        console.log('New offer received:', data);
        // Toast notification supprimée
      }
    );

    // Gestion de l'événement de nouveau message - NOTIFICATIONS SUPPRIMÉES
    socketInstance.on(
      'newMessage',
      (data: {
        senderFirstName: string;
        senderLastName: string;
        content: string;
      }) => {
        console.log('New message received:', data);
        // Toast notification supprimée
      }
    );

    // Gestion de l'événement de nouvelle proposition de meeting - NOTIFICATIONS SUPPRIMÉES
    socketInstance.on(
      'newMeetingProposal',
      (data: {
        proposalId: string;
        offerId: string;
        proposerId: string;
        receiverId: string;
        proposedMeetingTime: string;
        message?: string;
      }) => {
        console.log('New meeting proposal received:', data);
        // Toast notification supprimée
      }
    );

    // Gestion de l'événement de proposition acceptée - NOTIFICATIONS SUPPRIMÉES
    socketInstance.on(
      'meetingProposalAccepted',
      (data: {
        proposalId: string;
        offerId: string;
        acceptedBy: string;
        qrCodeData: string;
      }) => {
        console.log('Meeting proposal accepted:', data);
        // Toast notification supprimée
      }
    );

    // Gestion de l'événement de proposition déclinée - NOTIFICATIONS SUPPRIMÉES
    socketInstance.on(
      'meetingProposalDeclined',
      (data: { proposalId: string; offerId: string; declinedBy: string }) => {
        console.log('Meeting proposal declined:', data);
        // Toast notification supprimée
      }
    );

    setSocket(socketInstance);

    // Nettoyage à la déconnexion
    return () => {
      socketInstance.disconnect();
    };
  }, [toast]);

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
}
