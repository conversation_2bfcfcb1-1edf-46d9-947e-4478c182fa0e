import { z } from 'zod';

// Manual Zod schemas to replace drizzle-zod generated ones
export const settingsCreateDto = z.object({
  userId: z.string().uuid(),
  language: z.string().default('en'),
  darkMode: z.boolean().default(false),
  notificationsEnabled: z.boolean().default(true),
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
  profileVisibility: z.enum(['public', 'friends', 'private']).default('public'),
  locationSharing: z.boolean().default(true),
});
export type SettingsCreateDto = z.infer<typeof settingsCreateDto>;

export const settingsSelectDto = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  language: z.string(),
  darkMode: z.boolean(),
  notificationsEnabled: z.boolean(),
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  marketingEmails: z.boolean(),
  profileVisibility: z.enum(['public', 'friends', 'private']),
  locationSharing: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date().nullable(),
});
export type SettingsSelectDto = z.infer<typeof settingsSelectDto>;

export const settingsUpdateDto = z
  .object({
    language: z.string().optional(),
    darkMode: z.boolean().optional(),
    notificationsEnabled: z.boolean().optional(),
    emailNotifications: z.boolean().optional(),
    pushNotifications: z.boolean().optional(),
    marketingEmails: z.boolean().optional(),
    profileVisibility: z.enum(['public', 'friends', 'private']).optional(),
    locationSharing: z.boolean().optional(),
  })
  .partial();
export type SettingsUpdateDto = z.infer<typeof settingsUpdateDto>;

// Specific DTOs for different settings sections
export const notificationSettingsDto = z.object({
  notificationsEnabled: z.boolean(),
  emailNotifications: z.boolean(),
  pushNotifications: z.boolean(),
  marketingEmails: z.boolean(),
});
export type NotificationSettingsDto = z.infer<typeof notificationSettingsDto>;

export const privacySettingsDto = z.object({
  profileVisibility: z.enum(['public', 'friends', 'private']),
  locationSharing: z.boolean(),
});
export type PrivacySettingsDto = z.infer<typeof privacySettingsDto>;

export const appearanceSettingsDto = z.object({
  darkMode: z.boolean(),
  language: z.string(),
});
export type AppearanceSettingsDto = z.infer<typeof appearanceSettingsDto>;
