/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/categorySelectPage` | `/sign-up/categorySelectPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/otp` | `/sign-up/otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/profile` | `/sign-up/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/startPage` | `/sign-up/startPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/help` | `/help`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/language` | `/language`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/messages` | `/messages`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/payment-methods` | `/payment-methods`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/privacy` | `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/profile-edit` | `/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/subscribe` | `/subscribe`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/themeStore` | `/themeStore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-offers` | `/my-offers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-outgoing-offers` | `/my-outgoing-offers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/new` | `/new`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/gluestack-ui-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/navigation-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/react-query-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/socket-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/stripe-provider`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/needs/[id]` | `/needs/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(main)'}/offers/[id]` | `/offers/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up/categorySelectPage` | `/sign-up/categorySelectPage`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up/otp` | `/sign-up/otp`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up/profile` | `/sign-up/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up/startPage` | `/sign-up/startPage`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/help` | `/help`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/language` | `/language`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/messages` | `/messages`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/notifications` | `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/payment-methods` | `/payment-methods`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/privacy` | `/privacy`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/profile-edit` | `/profile-edit`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/subscribe` | `/subscribe`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/themeStore` | `/themeStore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-offers` | `/my-offers`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-outgoing-offers` | `/my-outgoing-offers`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/new` | `/new`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers/gluestack-ui-provider`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers/navigation-provider`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers/react-query-provider`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers/socket-provider`; params?: Router.UnknownOutputParams; } | { pathname: `/_providers/stripe-provider`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(main)'}/needs/[id]` | `/needs/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(main)'}/offers/[id]` | `/offers/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up/categorySelectPage${`?${string}` | `#${string}` | ''}` | `/sign-up/categorySelectPage${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up${`?${string}` | `#${string}` | ''}` | `/sign-up${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up/otp${`?${string}` | `#${string}` | ''}` | `/sign-up/otp${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up/profile${`?${string}` | `#${string}` | ''}` | `/sign-up/profile${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up/startPage${`?${string}` | `#${string}` | ''}` | `/sign-up/startPage${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/help${`?${string}` | `#${string}` | ''}` | `/help${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/language${`?${string}` | `#${string}` | ''}` | `/language${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/messages${`?${string}` | `#${string}` | ''}` | `/messages${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/notifications${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/payment-methods${`?${string}` | `#${string}` | ''}` | `/payment-methods${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/privacy${`?${string}` | `#${string}` | ''}` | `/privacy${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/profile-edit${`?${string}` | `#${string}` | ''}` | `/profile-edit${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/subscribe${`?${string}` | `#${string}` | ''}` | `/subscribe${`?${string}` | `#${string}` | ''}` | `${'/(main)'}/themeStore${`?${string}` | `#${string}` | ''}` | `/themeStore${`?${string}` | `#${string}` | ''}` | `${'/(main)'}${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(main)'}${'/(tabs)'}/my-offers${`?${string}` | `#${string}` | ''}` | `/my-offers${`?${string}` | `#${string}` | ''}` | `${'/(main)'}${'/(tabs)'}/my-outgoing-offers${`?${string}` | `#${string}` | ''}` | `/my-outgoing-offers${`?${string}` | `#${string}` | ''}` | `${'/(main)'}${'/(tabs)'}/new${`?${string}` | `#${string}` | ''}` | `/new${`?${string}` | `#${string}` | ''}` | `${'/(main)'}${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/_providers/gluestack-ui-provider${`?${string}` | `#${string}` | ''}` | `/_providers${`?${string}` | `#${string}` | ''}` | `/_providers/navigation-provider${`?${string}` | `#${string}` | ''}` | `/_providers/react-query-provider${`?${string}` | `#${string}` | ''}` | `/_providers/socket-provider${`?${string}` | `#${string}` | ''}` | `/_providers/stripe-provider${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/categorySelectPage` | `/sign-up/categorySelectPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/otp` | `/sign-up/otp`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/profile` | `/sign-up/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up/startPage` | `/sign-up/startPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/help` | `/help`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/language` | `/language`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/messages` | `/messages`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/notifications` | `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/payment-methods` | `/payment-methods`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/privacy` | `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/profile-edit` | `/profile-edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/subscribe` | `/subscribe`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}/themeStore` | `/themeStore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-offers` | `/my-offers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/my-outgoing-offers` | `/my-outgoing-offers`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/new` | `/new`; params?: Router.UnknownInputParams; } | { pathname: `${'/(main)'}${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/gluestack-ui-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/navigation-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/react-query-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/socket-provider`; params?: Router.UnknownInputParams; } | { pathname: `/_providers/stripe-provider`; params?: Router.UnknownInputParams; } | `${'/(main)'}/needs/${Router.SingleRoutePart<T>}` | `/needs/${Router.SingleRoutePart<T>}` | `${'/(main)'}/offers/${Router.SingleRoutePart<T>}` | `/offers/${Router.SingleRoutePart<T>}` | { pathname: `${'/(main)'}/needs/[id]` | `/needs/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(main)'}/offers/[id]` | `/offers/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
