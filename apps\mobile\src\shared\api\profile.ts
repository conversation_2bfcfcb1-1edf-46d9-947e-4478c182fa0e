import { nativeFetch } from '@/shared/lib';

export type UserProfile = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  imageRef?: string;
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
};

export type ProfileStats = {
  needsFulfilled: number;
  averageResponseTime: string;
  memberSince: string;
  totalEarnings: number;
  completedOffers: number;
  rating: number;
  reviewCount: number;
};

export type ProfileUpdateData = {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  email?: string;
};

/**
 * Get current user's profile
 */
export async function getProfile() {
  const response = await nativeFetch('/api/users/profile', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get profile');
  }

  const data = await response.json();
  return data.profile as UserProfile;
}

/**
 * Update current user's profile
 */
export async function updateProfile(profileData: ProfileUpdateData) {
  const response = await nativeFetch('/api/users/profile', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(profileData),
  });

  if (!response.ok) {
    throw new Error('Failed to update profile');
  }

  const data = await response.json();
  return data.profile as UserProfile;
}

/**
 * Update profile picture
 */
export async function updateProfilePicture(imageRef: string) {
  const response = await nativeFetch('/api/users/profile/picture', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ imageRef }),
  });

  if (!response.ok) {
    throw new Error('Failed to update profile picture');
  }

  const data = await response.json();
  return data.profile as UserProfile;
}

/**
 * Get profile statistics
 */
export async function getProfileStats() {
  const response = await nativeFetch('/api/users/profile/stats', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get profile stats');
  }

  const data = await response.json();
  return data.stats as ProfileStats;
}

/**
 * Get recent needs
 */
export async function getRecentNeeds() {
  const response = await nativeFetch('/api/users/profile/needs', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get recent needs');
  }

  const data = await response.json();
  return data.needs;
}

/**
 * Get recent offers
 */
export async function getRecentOffers() {
  const response = await nativeFetch('/api/users/profile/offers', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get recent offers');
  }

  const data = await response.json();
  return data.offers;
}

/**
 * Get activity timeline
 */
export async function getActivityTimeline() {
  const response = await nativeFetch('/api/users/profile/timeline', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Failed to get activity timeline');
  }

  const data = await response.json();
  return data.timeline;
}
