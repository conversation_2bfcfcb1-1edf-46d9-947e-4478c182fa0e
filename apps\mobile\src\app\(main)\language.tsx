import { useRouter } from 'expo-router';
import { Text, TouchableOpacity, ScrollView } from 'react-native';
import { useState, useEffect } from 'react';

import { ArrowLeft, Check, Globe } from 'lucide-react-native';

import {
  useSettings,
  useUpdateAppearanceSettings,
} from '@/shared/model/hooks/use-settings';

import { Box, VStack, HStack, Heading, SafeAreaView } from '@/shared/ui';

type Language = {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
};

const languages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
  { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹' },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands', flag: '🇳🇱' },
  { code: 'ru', name: 'Russian', nativeName: 'Русский', flag: '🇷🇺' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷' },
  { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦' },
];

export default function LanguagePage() {
  const router = useRouter();
  const { settings } = useSettings();
  const updateAppearanceSettings = useUpdateAppearanceSettings();

  const [selectedLanguage, setSelectedLanguage] = useState(
    settings?.language || 'en'
  );
  const [isUpdating, setIsUpdating] = useState(false);

  // Sync with backend settings
  useEffect(() => {
    if (settings?.language) {
      setSelectedLanguage(settings.language);
    }
  }, [settings]);

  const handleLanguageSelect = async (languageCode: string) => {
    setSelectedLanguage(languageCode);
    setIsUpdating(true);

    try {
      await updateAppearanceSettings.mutateAsync({
        language: languageCode,
        darkMode: settings?.darkMode ?? false,
      });

      // Optional: Show success message or restart app for language changes
      console.log(`Language changed to: ${languageCode}`);
    } catch (error) {
      console.error('Failed to update language:', error);
      // Revert on error
      setSelectedLanguage(settings?.language || 'en');
    } finally {
      setIsUpdating(false);
    }
  };

  const getCurrentLanguage = () => {
    return (
      languages.find((lang) => lang.code === selectedLanguage) || languages[0]
    );
  };

  const LanguageItem = ({ language }: { language: Language }) => {
    const isSelected = language.code === selectedLanguage;

    return (
      <TouchableOpacity
        onPress={() => handleLanguageSelect(language.code)}
        disabled={isUpdating}
        className={`bg-white rounded-xl shadow-sm border p-4 mb-3 ${
          isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-100'
        }`}
      >
        <HStack space="md" className="items-center">
          <Text className="text-2xl">{language.flag}</Text>

          <VStack className="flex-1" space="xs">
            <Text
              className={`font-semibold text-base ${
                isSelected ? 'text-blue-900' : 'text-gray-900'
              }`}
            >
              {language.name}
            </Text>
            <Text
              className={`text-sm ${
                isSelected ? 'text-blue-700' : 'text-gray-500'
              }`}
            >
              {language.nativeName}
            </Text>
          </VStack>

          {isSelected && (
            <Box className="w-6 h-6 bg-blue-600 rounded-full items-center justify-center">
              <Check size={16} color="white" />
            </Box>
          )}
        </HStack>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Language
            </Heading>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Current Language */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Current Language
            </Text>

            <HStack space="md" className="items-center">
              <Box className="w-12 h-12 bg-blue-100 rounded-xl items-center justify-center">
                <Globe size={24} color="#3B82F6" />
              </Box>
              <VStack className="flex-1">
                <Text className="text-gray-900 font-semibold text-lg">
                  {getCurrentLanguage().name}
                </Text>
                <Text className="text-gray-500">
                  {getCurrentLanguage().nativeName}
                </Text>
              </VStack>
              <Text className="text-3xl">{getCurrentLanguage().flag}</Text>
            </HStack>
          </Box>

          {/* Language List */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Available Languages
            </Text>

            {languages.map((language) => (
              <LanguageItem key={language.code} language={language} />
            ))}
          </VStack>

          {/* Info */}
          <Box className="bg-blue-50 rounded-xl p-4">
            <VStack space="sm">
              <Text className="text-blue-900 font-semibold">
                🌍 Language Settings
              </Text>
              <Text className="text-blue-800 text-sm">
                Changing your language will update the app interface. Some
                features may require an app restart to fully apply the new
                language.
              </Text>
            </VStack>
          </Box>

          {/* Tips */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Language Tips
            </Text>

            <VStack space="sm">
              <Text className="text-gray-600 text-sm">
                • Your language preference is synced across all your devices
              </Text>
              <Text className="text-gray-600 text-sm">
                • Dates, numbers, and currency will format according to your
                language
              </Text>
              <Text className="text-gray-600 text-sm">
                • You can change your language anytime in settings
              </Text>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>

      {/* Loading Overlay */}
      {isUpdating && (
        <Box className="absolute inset-0 bg-black bg-opacity-20 items-center justify-center">
          <Box className="bg-white rounded-xl p-6 items-center">
            <Text className="text-gray-900 font-semibold">
              Updating language...
            </Text>
          </Box>
        </Box>
      )}
    </SafeAreaView>
  );
}
