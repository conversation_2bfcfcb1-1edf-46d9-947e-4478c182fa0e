import React, { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import {
  ArrowLeft,
  MessageCircle,
  Send,
  Check,
  X,
  User,
  Clock,
  MapPin,
  ChevronDown,
  ChevronRight,
  Trash2,
  Apple,
  Dog,
  Car,
  Hammer,
  ShoppingBag,
  Home,
  Leaf,
  Laptop,
  Zap,
} from 'lucide-react-native';
import { useOfferStore } from '@/shared/model';
import { getUserNeeds, getCategories } from '@/pages/needs';
import { updateOffer } from '@/shared/api';
import type { NeedWithUser } from '@/pages/needs/api/get-user-needs';
import type { Offer as IncomingOffer } from '@/shared/api';
import type { Category } from '@/pages/needs/api/categories';
import {
  HStack,
  VStack,
  Heading,
  Divider,
  Icon,
  Box,
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
} from '@/shared/ui';

export default function MyOffers() {
  const router = useRouter();

  // This line can be used to produce reports to manage offers and requirements
  const {
    fetchIncomingOffers,
    incomingOffers,
    isLoading: isLoadingOffers,
    updateOffer: updateOfferInStore,
  } = useOfferStore();
  const [userNeeds, setUserNeeds] = useState<NeedWithUser[]>([]); // This line is the list of user requirements
  const [categories, setCategories] = useState<Category[]>([]); // List of categories
  const [isLoadingNeeds, setIsLoadingNeeds] = useState(false); // This line shows the loading status of the requirements
  const [isLoadingCategories, setIsLoadingCategories] = useState(false); // Loading status for categories
  const [error, setError] = useState<string | null>(null); // This line is used to manage errors
  const [expandedOffers, setExpandedOffers] = useState<Record<string, boolean>>(
    {}
  );
  const [seenOffers, setSeenOffers] = useState<Set<string>>(new Set()); // This line allows you to track offers that have already been consulted (via their ID)
  const [processingOffers, setProcessingOffers] = useState<Set<string>>(
    new Set()
  ); // Track offers being processed

  // Helper function to get category name from ID
  const getCategoryName = (categoryId: string | undefined) => {
    if (!categoryId || !categories) return 'general';
    const category = categories.find((cat) => cat.id === categoryId);
    return category?.name.toLowerCase() || 'general';
  };

  // Helper function to get category icon
  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return <Apple size={20} color="#10B981" />;
      case 'animals':
      case 'animaux':
        return <Dog size={20} color="#8B5CF6" />;
      case 'transport':
        return <Car size={20} color="#3B82F6" />;
      case 'diy':
      case 'bricolage':
        return <Hammer size={20} color="#F59E0B" />;
      case 'shopping':
      case 'courses':
        return <ShoppingBag size={20} color="#EF4444" />;
      case 'cleaning':
      case 'ménage':
        return <Home size={20} color="#06B6D4" />;
      case 'gardening':
      case 'jardinage':
        return <Leaf size={20} color="#84CC16" />;
      case 'technology':
      case 'gaming':
      case 'technologie':
        return <Laptop size={20} color="#6366F1" />;
      default:
        return <Zap size={20} color="#6B7280" />;
    }
  };

  // Helper function to get category color
  const getCategoryColor = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return '#10B981';
      case 'animals':
      case 'animaux':
        return '#8B5CF6';
      case 'transport':
        return '#3B82F6';
      case 'diy':
      case 'bricolage':
        return '#F59E0B';
      case 'shopping':
      case 'courses':
        return '#EF4444';
      case 'cleaning':
      case 'ménage':
        return '#06B6D4';
      case 'gardening':
      case 'jardinage':
        return '#84CC16';
      case 'technology':
      case 'gaming':
      case 'technologie':
        return '#6366F1';
      default:
        return '#6B7280';
    }
  };

  // Render delete button for swipe action
  const renderDeleteButton = (offerId: string) => {
    return (
      <TouchableOpacity
        onPress={() => handleRejectOffer(offerId)}
        className="bg-red-500 items-center justify-center w-20 rounded-r-xl"
      >
        <Icon as={Trash2} size="md" color="#FFFFFF" />
      </TouchableOpacity>
    );
  };

  // This line is the effect of loading the user's requirements at startup
  useEffect(() => {
    const fetchNeeds = async () => {
      setIsLoadingNeeds(true);
      try {
        const needs = await getUserNeeds();
        setUserNeeds(needs);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch needs');
      } finally {
        setIsLoadingNeeds(false);
      }
    };

    fetchNeeds(); // This line is used to call the function to load the requirements
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const fetchedCategories = await getCategories();
        setCategories(fetchedCategories);
      } catch (err) {
        console.error('Failed to fetch categories:', err);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // This line is used to load incoming offers
  useEffect(() => {
    fetchIncomingOffers();
  }, [fetchIncomingOffers]);

  // This line groups offers by need
  const offersByNeed: Record<string, IncomingOffer[]> = incomingOffers.reduce(
    (acc: Record<string, IncomingOffer[]>, offer: IncomingOffer) => {
      if (!acc[offer.needId]) {
        acc[offer.needId] = [];
      }
      acc[offer.needId].push(offer); // This line adds the offer to the corresponding requirement
      return acc;
    },
    {} as Record<string, IncomingOffer[]>
  );

  // This line calls the function for alternating the display of offers for a requirement (expand/collapse)
  const toggleNeedExpanded = (needId: string) => {
    setExpandedOffers((prev) => ({
      ...prev,
      [needId]: !prev[needId], // This line is used to reverse the expansion status of the requirement
    }));
  };

  // This line allows you to mark an offer as viewed and navigate to the details page.
  const handleOfferPress = (offerId: string) => {
    setSeenOffers((prev) => new Set(prev).add(offerId));
    router.push(`/messages?id=${offerId}` as any);
  };

  // Handle accepting an offer
  const handleAcceptOffer = async (offerId: string, needId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      // Accept the selected offer
      await updateOffer(offerId, { status: 'accepted' });

      // Update the local store
      // First, find all offers for this need
      const needOffers = incomingOffers.filter(
        (offer) => offer.needId === needId
      );

      // Update the status of all offers for this need
      for (const offer of needOffers) {
        if (offer.id === offerId) {
          // Mark the selected offer as accepted
          await updateOfferInStore(offer.id, { status: 'accepted' });
        } else {
          // Mark all other offers for this need as rejected
          await updateOfferInStore(offer.id, { status: 'rejected' });
        }
      }

      Alert.alert('Success', 'Offer accepted successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to accept offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // Handle rejecting an offer
  const handleRejectOffer = async (offerId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      await updateOffer(offerId, { status: 'rejected' });
      await updateOfferInStore(offerId, { status: 'rejected' });
      Alert.alert('Success', 'Offer rejected successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to reject offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // Loading state
  if (isLoadingNeeds || isLoadingOffers || isLoadingCategories) {
    return (
      <Box className="flex-1 bg-gray-50">
        <Box className="bg-white pt-20 pb-6">
          <Box className="px-6">
            <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
              <Heading className="text-2xl font-bold text-gray-900 text-center">
                My Offers
              </Heading>
            </Box>

            {/* Navigation Tabs */}
            <Box className="bg-gray-100 rounded-xl p-1">
              <HStack space="xs">
                <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={MessageCircle} size="sm" color="#3B82F6" />
                    <Text className="text-blue-600 font-bold text-sm">
                      Incoming
                    </Text>
                  </HStack>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => router.push('/my-outgoing-offers' as any)}
                  className="flex-1 py-3 rounded-lg"
                >
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={Send} size="sm" color="#6B7280" />
                    <Text className="text-gray-600 font-semibold text-sm">
                      Outgoing
                    </Text>
                  </HStack>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>
        <Box className="flex-1 justify-center items-center">
          <Spinner size="large" className="text-blue-600" />
          <Text className="mt-4 text-gray-600 font-medium">
            Loading your offers...
          </Text>
        </Box>
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box className="flex-1 bg-gray-50">
        <Box className="bg-white pt-20 pb-6">
          <Box className="px-6">
            <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
              <Heading className="text-2xl font-bold text-gray-900 text-center">
                My Offers
              </Heading>
            </Box>

            {/* Navigation Tabs */}
            <Box className="bg-gray-100 rounded-xl p-1">
              <HStack space="xs">
                <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={MessageCircle} size="sm" color="#3B82F6" />
                    <Text className="text-blue-600 font-bold text-sm">
                      Incoming
                    </Text>
                  </HStack>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => router.push('/my-outgoing-offers' as any)}
                  className="flex-1 py-3 rounded-lg"
                >
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={Send} size="sm" color="#6B7280" />
                    <Text className="text-gray-600 font-semibold text-sm">
                      Outgoing
                    </Text>
                  </HStack>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>
        <Box className="flex-1 justify-center items-center px-6">
          <Box className="bg-red-50 border border-red-200 rounded-xl p-6 w-full max-w-sm">
            <Text className="text-red-600 text-center font-medium">
              {error}
            </Text>
          </Box>
        </Box>
      </Box>
    );
  }

  // This line is used to obtain an avatar URL (demonstration)
  const getAvatarUrl = () => 'https://i.pravatar.cc/100';

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pt-20 pb-6">
        <Box className="px-6">
          {/* Title Card */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
            <Heading className="text-2xl font-bold text-gray-900 mb-2 text-center">
              My Offers
            </Heading>
            <Text className="text-gray-600 text-base font-medium text-center">
              Manage all your offers in one place
            </Text>
          </Box>

          {/* Navigation Tabs */}
          <Box className="bg-gray-100 rounded-xl p-1">
            <HStack space="xs">
              <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                <HStack className="items-center justify-center" space="xs">
                  <Icon as={MessageCircle} size="sm" color="#3B82F6" />
                  <Text className="text-blue-600 font-bold text-sm">
                    Incoming
                  </Text>
                </HStack>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => router.push('/my-outgoing-offers' as any)}
                className="flex-1 py-3 rounded-lg"
              >
                <HStack className="items-center justify-center" space="xs">
                  <Icon as={Send} size="sm" color="#6B7280" />
                  <Text className="text-gray-600 font-semibold text-sm">
                    Outgoing
                  </Text>
                </HStack>
              </TouchableOpacity>
            </HStack>
          </Box>
        </Box>
      </Box>

      {/* Content */}
      <ScrollView className="flex-1 px-6 pt-4">
        {userNeeds.map((need) => {
          const needOffers = offersByNeed[need.id] || [];
          const isExpanded = expandedOffers[need.id];
          const categoryName = getCategoryName(need.categoryId);
          const categoryColor = getCategoryColor(categoryName);

          return (
            <Box key={need.id} className="mb-6">
              {/* Need Card */}
              <TouchableOpacity
                onPress={() => toggleNeedExpanded(need.id)}
                className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
              >
                <Box className="p-5">
                  <HStack className="justify-between items-center">
                    <HStack className="items-center flex-1" space="md">
                      <Box
                        className="w-12 h-12 rounded-xl items-center justify-center"
                        style={{ backgroundColor: `${categoryColor}15` }}
                      >
                        {getCategoryIcon(categoryName)}
                      </Box>
                      <VStack className="flex-1" space="xs">
                        <Heading className="text-lg font-bold text-gray-900">
                          {need.title}
                        </Heading>
                        <HStack className="items-center" space="xs">
                          <Icon as={MapPin} size="sm" color="#9CA3AF" />
                          <Text className="text-gray-500 text-sm font-medium">
                            Paris • 2h ago
                          </Text>
                        </HStack>
                      </VStack>
                    </HStack>

                    <HStack className="items-center" space="sm">
                      <Box
                        className="px-3 py-1 rounded-full"
                        style={{ backgroundColor: `${categoryColor}15` }}
                      >
                        <Text
                          className="text-xs font-bold"
                          style={{ color: categoryColor }}
                        >
                          {needOffers.length}{' '}
                          {needOffers.length === 1 ? 'offer' : 'offers'}
                        </Text>
                      </Box>
                      <Icon
                        as={isExpanded ? ChevronDown : ChevronRight}
                        size="md"
                        color="#9CA3AF"
                      />
                    </HStack>
                  </HStack>
                </Box>
              </TouchableOpacity>

              {/* Offers List */}
              {isExpanded && (
                <VStack space="sm" className="mt-4">
                  {needOffers.map((offer) => {
                    const isProcessing = processingOffers.has(offer.id);
                    const isAccepted = offer.status === 'accepted';
                    const isRejected = offer.status === 'rejected';

                    return (
                      <Swipeable
                        key={offer.id}
                        renderRightActions={() => renderDeleteButton(offer.id)}
                        enabled={!isAccepted && !isRejected && !isProcessing}
                      >
                        <TouchableOpacity
                          onPress={() => handleOfferPress(offer.id)}
                          disabled={isProcessing}
                          className={`bg-white rounded-xl shadow-sm border overflow-hidden ${
                            isAccepted
                              ? 'border-green-200'
                              : isRejected
                              ? 'border-red-200'
                              : seenOffers.has(offer.id)
                              ? 'border-gray-200'
                              : 'border-blue-200'
                          }`}
                        >
                          {/* Accent bar */}
                          <Box
                            className="h-1 w-full"
                            style={{
                              backgroundColor: isAccepted
                                ? '#10B981'
                                : isRejected
                                ? '#EF4444'
                                : categoryColor,
                            }}
                          />

                          <Box className="p-5">
                            {/* Offer Header */}
                            <HStack className="justify-between items-center mb-4">
                              <HStack
                                className="items-center flex-1"
                                space="sm"
                              >
                                <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
                                  <Icon as={User} size="md" color="#6B7280" />
                                </Box>
                                <VStack className="flex-1" space="xs">
                                  <Text className="font-bold text-gray-900">
                                    {offer.user
                                      ? `${offer.user.firstName} ${offer.user.lastName}`
                                      : 'Unknown User'}
                                  </Text>
                                  <HStack className="items-center" space="xs">
                                    <Icon
                                      as={Clock}
                                      size="sm"
                                      color="#9CA3AF"
                                    />
                                    <Text className="text-gray-500 text-xs">
                                      5 min ago
                                    </Text>
                                  </HStack>
                                </VStack>
                              </HStack>

                              <Box className="w-10 h-10 bg-blue-100 rounded-xl items-center justify-center">
                                <Icon
                                  as={MessageCircle}
                                  size="md"
                                  color="#3B82F6"
                                />
                              </Box>
                            </HStack>

                            {/* Offer Content */}
                            <Box className="bg-gray-50 rounded-xl p-4 mb-4">
                              <Text className="text-gray-700 text-sm leading-relaxed">
                                {need.description}
                              </Text>
                            </Box>

                            {/* Price and Actions */}
                            <HStack className="justify-between items-center">
                              <VStack space="xs">
                                <Text className="text-2xl font-bold text-gray-900">
                                  €{offer.price}
                                </Text>
                                <Text className="text-gray-500 text-xs">
                                  Offered price
                                </Text>
                              </VStack>

                              {isProcessing ? (
                                <Box className="bg-gray-100 px-4 py-2 rounded-xl">
                                  <Spinner
                                    size="small"
                                    className="text-blue-600"
                                  />
                                </Box>
                              ) : isAccepted ? (
                                <Box className="bg-green-100 px-4 py-3 rounded-xl">
                                  <HStack className="items-center" space="xs">
                                    <Icon
                                      as={Check}
                                      size="sm"
                                      color="#10B981"
                                    />
                                    <Text className="text-green-700 font-bold">
                                      Accepted
                                    </Text>
                                  </HStack>
                                </Box>
                              ) : isRejected ? (
                                <Box className="bg-red-100 px-4 py-3 rounded-xl">
                                  <HStack className="items-center" space="xs">
                                    <Icon as={X} size="sm" color="#EF4444" />
                                    <Text className="text-red-700 font-bold">
                                      Rejected
                                    </Text>
                                  </HStack>
                                </Box>
                              ) : (
                                <HStack space="sm">
                                  <TouchableOpacity
                                    onPress={(e) => {
                                      e.stopPropagation();
                                      handleRejectOffer(offer.id);
                                    }}
                                    className="bg-gray-100 px-4 py-3 rounded-xl"
                                  >
                                    <HStack className="items-center" space="xs">
                                      <Icon as={X} size="sm" color="#6B7280" />
                                      <Text className="text-gray-700 font-bold">
                                        Decline
                                      </Text>
                                    </HStack>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={(e) => {
                                      e.stopPropagation();
                                      handleAcceptOffer(offer.id, need.id);
                                    }}
                                    className="bg-blue-600 px-4 py-3 rounded-xl shadow-sm"
                                  >
                                    <HStack className="items-center" space="xs">
                                      <Icon
                                        as={Check}
                                        size="sm"
                                        color="#FFFFFF"
                                      />
                                      <Text className="text-white font-bold">
                                        Accept
                                      </Text>
                                    </HStack>
                                  </TouchableOpacity>
                                </HStack>
                              )}
                            </HStack>
                          </Box>
                        </TouchableOpacity>
                      </Swipeable>
                    );
                  })}
                </VStack>
              )}
            </Box>
          );
        })}

        {/* Empty state */}
        {userNeeds.length === 0 && (
          <Box className="flex-1 justify-center items-center py-16 px-6">
            <Box className="w-20 h-20 bg-gray-100 rounded-2xl items-center justify-center mb-6">
              <Icon as={Send} size="xl" color="#9CA3AF" />
            </Box>
            <Heading className="text-xl font-bold text-gray-900 mb-2 text-center">
              No offers yet
            </Heading>
            <Text className="text-gray-500 text-center text-base leading-6 max-w-sm">
              Create a need to start receiving offers from the community
            </Text>
          </Box>
        )}
      </ScrollView>
    </Box>
  );
}
