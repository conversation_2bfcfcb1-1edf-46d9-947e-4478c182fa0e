import { Hono } from 'hono';
import { z<PERSON>alidator } from '@hono/zod-validator';
import { z } from 'zod';

import { meetingProposalService } from './meeting-proposal.service';
import {
  meetingProposalCreateDto,
  meetingProposalUpdateDto,
} from './meeting-proposal.dto';

import type { AuthVariables } from '@/shared/model';

export const meetingProposalRoutes = new Hono<{
  Variables: AuthVariables;
}>()
  // Créer une proposition de meeting
  .post('/', zValidator('json', meetingProposalCreateDto), async (c) => {
    const body = c.req.valid('json');
    const session = c.get('session')!;
    const user = session.user;

    try {
      const proposal = await meetingProposalService.createProposal(
        body,
        user.id
      );
      return c.json({ proposal });
    } catch (error) {
      return c.json(
        { error: error instanceof Error ? error.message : 'Unknown error' },
        400
      );
    }
  })
  // Obtenir les propositions pour une offre
  .get(
    '/offer/:offerId',
    zValidator('param', z.object({ offerId: z.string().uuid() })),
    async (c) => {
      const { offerId } = c.req.valid('param');
      const session = c.get('session')!;

      try {
        const proposals = await meetingProposalService.getProposalsByOfferId(
          offerId
        );
        return c.json({ proposals });
      } catch (error) {
        return c.json(
          { error: error instanceof Error ? error.message : 'Unknown error' },
          400
        );
      }
    }
  )
  // Obtenir une proposition par ID
  .get(
    '/:proposalId',
    zValidator('param', z.object({ proposalId: z.string().uuid() })),
    async (c) => {
      const { proposalId } = c.req.valid('param');
      const session = c.get('session')!;

      try {
        const proposal = await meetingProposalService.getProposalById(
          proposalId
        );
        if (!proposal) {
          return c.json({ error: 'Meeting proposal not found' }, 404);
        }
        return c.json({ proposal });
      } catch (error) {
        return c.json(
          { error: error instanceof Error ? error.message : 'Unknown error' },
          400
        );
      }
    }
  )
  // Accepter une proposition
  .patch(
    '/:proposalId/accept',
    zValidator('param', z.object({ proposalId: z.string().uuid() })),
    async (c) => {
      const { proposalId } = c.req.valid('param');
      const session = c.get('session')!;
      const user = session.user;

      try {
        const proposal = await meetingProposalService.acceptProposal(
          proposalId,
          user.id
        );
        return c.json({ proposal });
      } catch (error) {
        return c.json(
          { error: error instanceof Error ? error.message : 'Unknown error' },
          400
        );
      }
    }
  )
  // Décliner une proposition
  .patch(
    '/:proposalId/decline',
    zValidator('param', z.object({ proposalId: z.string().uuid() })),
    async (c) => {
      const { proposalId } = c.req.valid('param');
      const session = c.get('session')!;
      const user = session.user;

      try {
        const proposal = await meetingProposalService.declineProposal(
          proposalId,
          user.id
        );
        return c.json({ proposal });
      } catch (error) {
        return c.json(
          { error: error instanceof Error ? error.message : 'Unknown error' },
          400
        );
      }
    }
  );
