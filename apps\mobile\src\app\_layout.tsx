import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';

import './global.css';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import {
  Gluestack<PERSON>Provider,
  ReactQueryProvider,
  SocketProvider,
} from './_providers';

// Comment out splash screen prevention for now
// SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  // Remove font loading logic
  // const [fontsLoaded] = useFonts({
  //   Inter_400Regular,
  //   Inter_500Medium,
  //   Inter_600SemiBold,
  //   Inter_700Bold,
  // });

  // useEffect(() => {
  //   if (fontsLoaded) {
  //     SplashScreen.hideAsync();
  //   }
  // }, [fontsLoaded]);

  // if (!fontsLoaded) {
  //   return null;
  // }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <GluestackUIProvider mode="light">
        <ReactQueryProvider>
          <SocketProvider>
            <Stack>
              <Stack.Screen name="(main)" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
            </Stack>
          </SocketProvider>
        </ReactQueryProvider>
      </GluestackUIProvider>
    </GestureHandlerRootView>
  );
}
