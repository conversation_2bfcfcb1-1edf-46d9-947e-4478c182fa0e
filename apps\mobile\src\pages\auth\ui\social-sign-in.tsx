import React, { useState, useEffect, useRef } from 'react';
import { TouchableOpacity, Platform, Alert } from 'react-native';
import { HStack, VStack, Box, Text, Spinner } from '@/shared/ui';

interface SocialSignInProps {
  onGooglePress?: () => void;
  onApplePress?: () => void;
}

export function SocialSignIn({
  onGooglePress,
  onApplePress,
}: SocialSignInProps) {
  const [loadingProvider, setLoadingProvider] = useState<
    'google' | 'apple' | null
  >(null);
  const isMountedRef = useRef(true);

  let router: any;
  try {
    const { useRouter } = require('expo-router');
    router = useRouter();
  } catch (error) {
    console.log('Router not ready yet');
  }

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleGoogleSignIn = async () => {
    try {
      setLoadingProvider('google');

      if (onGooglePress) {
        onGooglePress();
        return;
      }

      // Pour l'instant, juste un placeholder
      setTimeout(() => {
        setLoadingProvider(null);
        Alert.alert('Coming Soon', 'Google Sign-In will be available soon.');
      }, 1000);
    } catch (error) {
      console.error('Google Sign-In Error:', error);
      setLoadingProvider(null);
    }
  };

  const handleAppleSignIn = async () => {
    setLoadingProvider('apple');

    if (onApplePress) {
      onApplePress();
    }

    // Pour l'instant, juste un placeholder
    setTimeout(() => {
      setLoadingProvider(null);
      Alert.alert('Coming Soon', 'Apple Sign-In will be available soon.');
    }, 1000);
  };

  return (
    <VStack space="md" className="w-full">
      {/* Google Sign-In Button */}
      <TouchableOpacity
        onPress={handleGoogleSignIn}
        disabled={loadingProvider !== null}
        className="w-full"
      >
        <Box className="bg-white border border-gray-200 rounded-xl h-14 flex-row items-center justify-center px-4 shadow-sm">
          {loadingProvider === 'google' ? (
            <HStack className="items-center" space="sm">
              <Spinner size="small" color="#4285F4" />
              <Text className="text-gray-700 font-semibold text-base">
                Connecting...
              </Text>
            </HStack>
          ) : (
            <Text className="text-gray-700 font-semibold text-base">
              Continue with Google
            </Text>
          )}
        </Box>
      </TouchableOpacity>

      {/* Apple Sign-In Button (iOS only) */}
      {Platform.OS === 'ios' && (
        <TouchableOpacity
          onPress={handleAppleSignIn}
          disabled={loadingProvider !== null}
          className="w-full"
        >
          <Box className="bg-black rounded-xl h-14 flex-row items-center justify-center px-4 shadow-sm">
            {loadingProvider === 'apple' ? (
              <HStack className="items-center" space="sm">
                <Spinner size="small" color="white" />
                <Text className="text-white font-semibold text-base">
                  Connecting...
                </Text>
              </HStack>
            ) : (
              <Text className="text-white font-semibold text-base">
                Continue with Apple
              </Text>
            )}
          </Box>
        </TouchableOpacity>
      )}
    </VStack>
  );
}
