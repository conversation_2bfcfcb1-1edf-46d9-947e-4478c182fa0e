import { meetingProposalRepository } from './meeting-proposal.repository';
import { offerRepository } from '@/modules/offers';
import { socket } from '@/socket';

import type { MeetingProposalCreateDto } from './meeting-proposal.dto';

export const meetingProposalService = {
  async createProposal(data: MeetingProposalCreateDto, proposerId: string) {
    // Vérifier que l'offre existe
    const offer = await offerRepository.findById(data.offerId);
    if (!offer) {
      throw new Error('Offer not found');
    }

    // Déterminer le receiver (si proposer est owner de l'offre, receiver est owner du need, et vice versa)
    let receiverId: string;
    if (offer.userId === proposerId) {
      // Le proposer est l'owner de l'offre, donc receiver est l'owner du need
      receiverId = offer.need?.userId || '';
    } else {
      // Le proposer n'est pas l'owner de l'offre, donc receiver est l'owner de l'offre
      receiverId = offer.userId;
    }

    if (!receiverId) {
      throw new Error('Unable to determine receiver');
    }

    // Vérifier qu'il n'y a pas déjà une proposal active
    const activeProposals = await meetingProposalRepository.findActiveByOfferId(
      data.offerId
    );
    if (activeProposals.length > 0) {
      throw new Error(
        'There is already an active meeting proposal for this offer'
      );
    }

    // Créer la proposal avec expiration automatique (24h par défaut)
    const expiresAt =
      data.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000);

    const proposal = await meetingProposalRepository.create({
      ...data,
      proposerId,
      receiverId,
      expiresAt,
    });

    // Émettre un événement socket pour notifier le receiver
    socket.emit('newMeetingProposal', {
      proposalId: proposal.id,
      offerId: data.offerId,
      proposerId,
      receiverId,
      proposedMeetingTime: data.proposedMeetingTime.toISOString(),
      message: data.message,
    });

    return proposal;
  },

  async getProposalsByOfferId(offerId: string) {
    return await meetingProposalRepository.findByOfferId(offerId);
  },

  async getProposalById(id: string) {
    return await meetingProposalRepository.findById(id);
  },

  async acceptProposal(proposalId: string, userId: string) {
    const proposal = await meetingProposalRepository.findById(proposalId);
    if (!proposal) {
      throw new Error('Meeting proposal not found');
    }

    if (proposal.receiverId !== userId) {
      throw new Error('You are not authorized to accept this proposal');
    }

    if (proposal.status !== 'pending') {
      throw new Error('This proposal is no longer pending');
    }

    // Générer les données QR code stables
    const qrCodeData = JSON.stringify({
      id: proposalId,
      offerId: proposal.offerId,
      proposedMeetingTime: proposal.proposedMeetingTime,
      proposerId: proposal.proposerId,
      receiverId: proposal.receiverId,
      acceptedAt: new Date().toISOString(),
    });

    const updatedProposal = await meetingProposalRepository.updateStatus(
      proposalId,
      'accepted',
      qrCodeData
    );

    // Émettre un événement socket pour notifier le proposer
    socket.emit('meetingProposalAccepted', {
      proposalId,
      offerId: proposal.offerId,
      acceptedBy: userId,
      qrCodeData,
    });

    return updatedProposal;
  },

  async declineProposal(proposalId: string, userId: string) {
    const proposal = await meetingProposalRepository.findById(proposalId);
    if (!proposal) {
      throw new Error('Meeting proposal not found');
    }

    if (proposal.receiverId !== userId) {
      throw new Error('You are not authorized to decline this proposal');
    }

    if (proposal.status !== 'pending') {
      throw new Error('This proposal is no longer pending');
    }

    const updatedProposal = await meetingProposalRepository.updateStatus(
      proposalId,
      'declined'
    );

    // Émettre un événement socket pour notifier le proposer
    socket.emit('meetingProposalDeclined', {
      proposalId,
      offerId: proposal.offerId,
      declinedBy: userId,
    });

    return updatedProposal;
  },

  async expireOldProposals() {
    return await meetingProposalRepository.expireOldProposals();
  },
};
