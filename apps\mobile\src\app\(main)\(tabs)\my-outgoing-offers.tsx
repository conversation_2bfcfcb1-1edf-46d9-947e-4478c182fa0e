import React, { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
  Animated,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import {
  ArrowLeft,
  MessageCircle,
  Send,
  Check,
  X,
  User,
  Clock,
  MapPin,
  ChevronDown,
  ChevronRight,
  Trash2,
  Apple,
  Dog,
  Car,
  Hammer,
  ShoppingBag,
  Home,
  Leaf,
  Laptop,
  Zap,
} from 'lucide-react-native';
import { useOfferStore } from '@/shared/model';
import { updateOffer } from '@/shared/api';
import type { Offer } from '@/shared/api';
import {
  HStack,
  VStack,
  Heading,
  Divider,
  Icon,
  Box,
  Button,
  ButtonText,
  Spinner,
} from '@/shared/ui';

export default function MyOutgoingOffers() {
  const router = useRouter();

  const {
    fetchOutgoingOffers,
    outgoingOffers,
    isLoading: isLoadingOffers,
    updateOffer: updateOfferInStore,
    reset: resetOfferStore,
  } = useOfferStore();

  const [error, setError] = useState<string | null>(null);
  const [expandedOffers, setExpandedOffers] = useState<Record<string, boolean>>(
    {}
  );
  const [processingOffers, setProcessingOffers] = useState<Set<string>>(
    new Set()
  );

  // Helper function to get category icon
  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName?.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return <Apple size={20} color="#10B981" />;
      case 'animals':
      case 'animaux':
        return <Dog size={20} color="#8B5CF6" />;
      case 'transport':
        return <Car size={20} color="#3B82F6" />;
      case 'diy':
      case 'bricolage':
        return <Hammer size={20} color="#F59E0B" />;
      case 'shopping':
      case 'courses':
        return <ShoppingBag size={20} color="#EF4444" />;
      case 'cleaning':
      case 'ménage':
        return <Home size={20} color="#06B6D4" />;
      case 'gardening':
      case 'jardinage':
        return <Leaf size={20} color="#84CC16" />;
      case 'technology':
      case 'gaming':
      case 'technologie':
        return <Laptop size={20} color="#6366F1" />;
      default:
        return <Zap size={20} color="#6B7280" />;
    }
  };

  // Helper function to get category color
  const getCategoryColor = (categoryName: string) => {
    switch (categoryName?.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return '#10B981';
      case 'animals':
      case 'animaux':
        return '#8B5CF6';
      case 'transport':
        return '#3B82F6';
      case 'diy':
      case 'bricolage':
        return '#F59E0B';
      case 'shopping':
      case 'courses':
        return '#EF4444';
      case 'cleaning':
      case 'ménage':
        return '#06B6D4';
      case 'gardening':
      case 'jardinage':
        return '#84CC16';
      case 'technology':
      case 'gaming':
      case 'technologie':
        return '#6366F1';
      default:
        return '#6B7280';
    }
  };

  // Load outgoing offers
  useEffect(() => {
    fetchOutgoingOffers();
  }, [fetchOutgoingOffers]);

  // Group offers by need
  const offersByNeed: Record<string, Offer[]> = outgoingOffers.reduce(
    (acc: Record<string, Offer[]>, offer: Offer) => {
      if (!acc[offer.needId]) {
        acc[offer.needId] = [];
      }
      acc[offer.needId].push(offer);
      return acc;
    },
    {} as Record<string, Offer[]>
  );

  // Toggle expanded state for a need
  const toggleNeedExpanded = (needId: string) => {
    setExpandedOffers((prev) => ({
      ...prev,
      [needId]: !prev[needId],
    }));
  };

  // Navigate to offer detail
  const handleOfferPress = (offerId: string) => {
    router.push(`/offers/${offerId}` as any);
  };

  // Cancel an offer
  const handleCancelOffer = async (offerId: string) => {
    setProcessingOffers((prev) => new Set(prev).add(offerId));
    try {
      await updateOffer(offerId, { status: 'rejected' });
      await updateOfferInStore(offerId, { status: 'rejected' });
      Alert.alert('Success', 'Offer cancelled successfully');
    } catch (err) {
      Alert.alert(
        'Error',
        err instanceof Error ? err.message : 'Failed to cancel offer'
      );
    } finally {
      setProcessingOffers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(offerId);
        return newSet;
      });
    }
  };

  // Loading state
  if (isLoadingOffers) {
    return (
      <Box className="flex-1 bg-gray-50">
        <Box className="bg-white pt-20 pb-6">
          <Box className="px-6">
            <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
              <Heading className="text-2xl font-bold text-gray-900 text-center">
                My Offers
              </Heading>
            </Box>

            {/* Navigation Tabs */}
            <Box className="bg-gray-100 rounded-xl p-1">
              <HStack space="xs">
                <TouchableOpacity
                  onPress={() => router.push('/my-offers' as any)}
                  className="flex-1 py-3 rounded-lg"
                >
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={MessageCircle} size="sm" color="#6B7280" />
                    <Text className="text-gray-600 font-semibold text-sm">
                      Incoming
                    </Text>
                  </HStack>
                </TouchableOpacity>
                <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={Send} size="sm" color="#3B82F6" />
                    <Text className="text-blue-600 font-bold text-sm">
                      Outgoing
                    </Text>
                  </HStack>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>
        <Box className="flex-1 justify-center items-center">
          <Spinner size="large" className="text-blue-600" />
          <Text className="mt-4 text-gray-600 font-medium">
            Loading your offers...
          </Text>
        </Box>
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Box className="flex-1 bg-gray-50">
        <Box className="bg-white pt-20 pb-6">
          <Box className="px-6">
            <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
              <Heading className="text-2xl font-bold text-gray-900 text-center">
                My Offers
              </Heading>
            </Box>

            {/* Navigation Tabs */}
            <Box className="bg-gray-100 rounded-xl p-1">
              <HStack space="xs">
                <TouchableOpacity
                  onPress={() => router.push('/my-offers' as any)}
                  className="flex-1 py-3 rounded-lg"
                >
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={MessageCircle} size="sm" color="#6B7280" />
                    <Text className="text-gray-600 font-semibold text-sm">
                      Incoming
                    </Text>
                  </HStack>
                </TouchableOpacity>
                <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={Send} size="sm" color="#3B82F6" />
                    <Text className="text-blue-600 font-bold text-sm">
                      Outgoing
                    </Text>
                  </HStack>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>
        <Box className="flex-1 justify-center items-center px-6">
          <Box className="bg-red-50 border border-red-200 rounded-xl p-6 w-full max-w-sm">
            <Text className="text-red-600 text-center font-medium">
              {error}
            </Text>
          </Box>
        </Box>
      </Box>
    );
  }

  // Empty state
  if (outgoingOffers.length === 0) {
    return (
      <Box className="flex-1 bg-gray-50">
        {/* Header */}
        <Box className="bg-white pt-20 pb-6">
          <Box className="px-6">
            {/* Title Card */}
            <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
              <Heading className="text-2xl font-bold text-gray-900 mb-2 text-center">
                My Offers
              </Heading>
              <Text className="text-gray-600 text-base font-medium text-center">
                Manage all your offers in one place
              </Text>
            </Box>

            {/* Navigation Tabs */}
            <Box className="bg-gray-100 rounded-xl p-1">
              <HStack space="xs">
                <TouchableOpacity
                  onPress={() => router.push('/my-offers' as any)}
                  className="flex-1 py-3 rounded-lg"
                >
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={MessageCircle} size="sm" color="#6B7280" />
                    <Text className="text-gray-600 font-semibold text-sm">
                      Incoming
                    </Text>
                  </HStack>
                </TouchableOpacity>
                <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                  <HStack className="items-center justify-center" space="xs">
                    <Icon as={Send} size="sm" color="#3B82F6" />
                    <Text className="text-blue-600 font-bold text-sm">
                      Outgoing
                    </Text>
                  </HStack>
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>

        {/* Empty State */}
        <Box className="flex-1 justify-center items-center py-16 px-6">
          <Box className="w-20 h-20 bg-gray-100 rounded-2xl items-center justify-center mb-6">
            <Icon as={Send} size="xl" color="#9CA3AF" />
          </Box>
          <Heading className="text-xl font-bold text-gray-900 mb-3 text-center">
            No offers made yet
          </Heading>
          <Text className="text-gray-500 text-center text-base leading-6 max-w-sm">
            Browse community needs and make your first offer to help someone
          </Text>
        </Box>
      </Box>
    );
  }

  // Render delete button for swipe action
  const renderDeleteButton = (offerId: string) => {
    return (
      <TouchableOpacity
        onPress={() => handleCancelOffer(offerId)}
        className="bg-red-500 items-center justify-center w-20 rounded-r-xl"
      >
        <Icon as={Trash2} size="md" color="#FFFFFF" />
      </TouchableOpacity>
    );
  };

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pt-20 pb-6">
        <Box className="px-6">
          {/* Title Card */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
            <Heading className="text-2xl font-bold text-gray-900 mb-2 text-center">
              My Offers
            </Heading>
            <Text className="text-gray-600 text-base font-medium text-center">
              Manage all your offers in one place
            </Text>
            {/* Debug button - temporary */}
            <TouchableOpacity
              onPress={async () => {
                console.log('🔄 Debug: Refreshing outgoing offers...');
                resetOfferStore();
                await fetchOutgoingOffers();
              }}
              className="mt-2 bg-orange-500 px-3 py-1 rounded"
            >
              <Text className="text-white text-xs text-center">
                DEBUG: Refresh
              </Text>
            </TouchableOpacity>
          </Box>

          {/* Navigation Tabs */}
          <Box className="bg-gray-100 rounded-xl p-1">
            <HStack space="xs">
              <TouchableOpacity
                onPress={() => router.push('/my-offers' as any)}
                className="flex-1 py-3 rounded-lg"
              >
                <HStack className="items-center justify-center" space="xs">
                  <Icon as={MessageCircle} size="sm" color="#6B7280" />
                  <Text className="text-gray-600 font-semibold text-sm">
                    Incoming
                  </Text>
                </HStack>
              </TouchableOpacity>
              <TouchableOpacity className="flex-1 bg-white py-3 rounded-lg shadow-sm">
                <HStack className="items-center justify-center" space="xs">
                  <Icon as={Send} size="sm" color="#3B82F6" />
                  <Text className="text-blue-600 font-bold text-sm">
                    Outgoing
                  </Text>
                </HStack>
              </TouchableOpacity>
            </HStack>
          </Box>
        </Box>
      </Box>

      {/* Content */}
      <ScrollView className="flex-1 px-6 pt-4">
        {Object.entries(offersByNeed).map(([needId, offers]) => {
          const need = offers[0]?.need;
          const isExpanded = expandedOffers[needId];
          const categoryColor = getCategoryColor(need?.title || 'general');

          if (!need) return null;

          return (
            <Box key={needId} className="mb-6">
              {/* Need Card */}
              <TouchableOpacity
                onPress={() => toggleNeedExpanded(needId)}
                className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
              >
                <Box className="p-5">
                  <HStack className="justify-between items-center">
                    <HStack className="items-center flex-1" space="md">
                      <Box
                        className="w-12 h-12 rounded-xl items-center justify-center"
                        style={{ backgroundColor: `${categoryColor}15` }}
                      >
                        {getCategoryIcon(need.title)}
                      </Box>
                      <VStack className="flex-1" space="xs">
                        <Heading className="text-lg font-bold text-gray-900">
                          {need.title}
                        </Heading>
                        <HStack className="items-center" space="xs">
                          <Icon as={MapPin} size="sm" color="#9CA3AF" />
                          <Text className="text-gray-500 text-sm font-medium">
                            Your offers to this need
                          </Text>
                        </HStack>
                      </VStack>
                    </HStack>

                    <HStack className="items-center" space="sm">
                      <Box
                        className="px-3 py-1 rounded-full"
                        style={{ backgroundColor: `${categoryColor}15` }}
                      >
                        <Text
                          className="text-xs font-bold"
                          style={{ color: categoryColor }}
                        >
                          {offers.length}{' '}
                          {offers.length === 1 ? 'offer' : 'offers'}
                        </Text>
                      </Box>
                      <Icon
                        as={isExpanded ? ChevronDown : ChevronRight}
                        size="md"
                        color="#9CA3AF"
                      />
                    </HStack>
                  </HStack>
                </Box>
              </TouchableOpacity>

              {/* Offers List */}
              {isExpanded && (
                <VStack space="sm" className="mt-4">
                  {offers.map((offer) => {
                    const isProcessing = processingOffers.has(offer.id);
                    const isAccepted = offer.status === 'accepted';
                    const isRejected = offer.status === 'rejected';

                    return (
                      <Swipeable
                        key={offer.id}
                        renderRightActions={() => renderDeleteButton(offer.id)}
                        enabled={!isAccepted && !isRejected && !isProcessing}
                      >
                        <TouchableOpacity
                          onPress={() => handleOfferPress(offer.id)}
                          disabled={isProcessing}
                          className={`bg-white rounded-xl shadow-sm border overflow-hidden ${
                            isAccepted
                              ? 'border-green-200'
                              : isRejected
                              ? 'border-red-200'
                              : 'border-orange-200'
                          }`}
                        >
                          {/* Accent bar */}
                          <Box
                            className="h-1 w-full"
                            style={{
                              backgroundColor: isAccepted
                                ? '#10B981'
                                : isRejected
                                ? '#EF4444'
                                : categoryColor,
                            }}
                          />

                          <Box className="p-5">
                            {/* Offer Header */}
                            <HStack className="justify-between items-center mb-4">
                              <HStack
                                className="items-center flex-1"
                                space="sm"
                              >
                                <Box className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
                                  <Icon as={User} size="md" color="#6B7280" />
                                </Box>
                                <VStack className="flex-1" space="xs">
                                  <Text className="font-bold text-gray-900">
                                    You offered to help
                                  </Text>
                                  <HStack className="items-center" space="xs">
                                    <Icon
                                      as={Clock}
                                      size="sm"
                                      color="#9CA3AF"
                                    />
                                    <Text className="text-gray-500 text-xs">
                                      Status:{' '}
                                      {isAccepted
                                        ? 'Accepted'
                                        : isRejected
                                        ? 'Rejected/Cancelled'
                                        : 'Pending'}
                                    </Text>
                                  </HStack>
                                </VStack>
                              </HStack>

                              <Box className="w-10 h-10 bg-orange-100 rounded-xl items-center justify-center">
                                <Icon
                                  as={MessageCircle}
                                  size="md"
                                  color="#F59E0B"
                                />
                              </Box>
                            </HStack>

                            {/* Offer Content */}
                            <Box className="bg-gray-50 rounded-xl p-4 mb-4">
                              <Text className="text-gray-700 text-sm leading-relaxed">
                                {need.description}
                              </Text>
                            </Box>

                            {/* Price and Actions */}
                            <HStack className="justify-between items-center">
                              <VStack space="xs">
                                <Text className="text-2xl font-bold text-gray-900">
                                  €{offer.price}
                                </Text>
                                <Text className="text-gray-500 text-xs">
                                  Your offer
                                </Text>
                              </VStack>

                              {isProcessing ? (
                                <Box className="bg-gray-100 px-4 py-2 rounded-xl">
                                  <Spinner
                                    size="small"
                                    className="text-blue-600"
                                  />
                                </Box>
                              ) : isAccepted ? (
                                <Box className="bg-green-100 px-4 py-3 rounded-xl">
                                  <HStack className="items-center" space="xs">
                                    <Icon
                                      as={Check}
                                      size="sm"
                                      color="#10B981"
                                    />
                                    <Text className="text-green-700 font-bold">
                                      Accepted by recipient
                                    </Text>
                                  </HStack>
                                </Box>
                              ) : isRejected ? (
                                <Box className="bg-red-100 px-4 py-3 rounded-xl">
                                  <HStack className="items-center" space="xs">
                                    <Icon as={X} size="sm" color="#EF4444" />
                                    <Text className="text-red-700 font-bold">
                                      Cancelled/Rejected
                                    </Text>
                                  </HStack>
                                </Box>
                              ) : (
                                <TouchableOpacity
                                  onPress={(e) => {
                                    e.stopPropagation();
                                    handleCancelOffer(offer.id);
                                  }}
                                  className="bg-red-500 px-4 py-3 rounded-xl shadow-sm"
                                >
                                  <HStack className="items-center" space="xs">
                                    <Icon as={X} size="sm" color="#FFFFFF" />
                                    <Text className="text-white font-bold">
                                      Cancel Offer
                                    </Text>
                                  </HStack>
                                </TouchableOpacity>
                              )}
                            </HStack>
                          </Box>
                        </TouchableOpacity>
                      </Swipeable>
                    );
                  })}
                </VStack>
              )}
            </Box>
          );
        })}
      </ScrollView>
    </Box>
  );
}
