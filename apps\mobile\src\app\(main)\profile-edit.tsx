import { useRouter } from 'expo-router';
import { Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useState, useEffect } from 'react';

import {
  ArrowLeft,
  Camera,
  User,
  Mail,
  Phone,
  Save,
} from 'lucide-react-native';

import {
  useAuth,
  useProfile,
  useUpdateProfile,
  useUpdateProfilePicture,
} from '@/shared/model';
import {
  Box,
  VStack,
  HStack,
  Heading,
  SafeAreaView,
  Avatar,
  AvatarFallbackText,
  AvatarImage,
  Input,
  InputField,
  Button,
  ButtonText,
} from '@/shared/ui';

export default function ProfileEdit() {
  const router = useRouter();
  const { session } = useAuth();

  // Profile hooks
  const { profile, isLoading: isProfileLoading } = useProfile();
  const updateProfileMutation = useUpdateProfile();
  const updateProfilePictureMutation = useUpdateProfilePicture();

  // Form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  // Sync with profile data (prioritize profile data over session data)
  useEffect(() => {
    if (profile) {
      setFirstName(profile.firstName || '');
      setLastName(profile.lastName || '');
      setEmail(profile.email || '');
      setPhoneNumber(profile.phoneNumber || '');
    } else if (session?.user) {
      setFirstName(session.user.firstName || '');
      setLastName(session.user.lastName || '');
      setEmail(session.user.email || '');
      setPhoneNumber(session.user.phoneNumber || '');
    }
  }, [profile, session]);

  const handleSave = async () => {
    try {
      await updateProfileMutation.mutateAsync({
        firstName,
        lastName,
        email,
        phoneNumber,
      });

      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };

  const handleChangePhoto = () => {
    Alert.alert('Change Photo', 'Choose an option', [
      { text: 'Camera', onPress: () => console.log('Open camera') },
      { text: 'Gallery', onPress: () => console.log('Open gallery') },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center justify-between">
            <HStack space="md" className="items-center flex-1">
              <TouchableOpacity
                onPress={() => router.back()}
                className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
              >
                <ArrowLeft size={20} color="#6B7280" />
              </TouchableOpacity>
              <Heading className="text-xl font-bold text-gray-900">
                Edit Profile
              </Heading>
            </HStack>
            <TouchableOpacity
              onPress={handleSave}
              disabled={updateProfileMutation.isPending}
              className={`px-4 py-2 rounded-xl ${
                updateProfileMutation.isPending ? 'bg-gray-300' : 'bg-blue-600'
              }`}
            >
              <Text className="text-white font-semibold">
                {updateProfileMutation.isPending ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Profile Photo Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Profile Photo
            </Text>

            <VStack space="md" className="items-center">
              <Box className="relative">
                <Avatar size="2xl">
                  <AvatarFallbackText>
                    {`${firstName} ${lastName}`}
                  </AvatarFallbackText>
                  <AvatarImage
                    source={{
                      uri: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
                    }}
                  />
                </Avatar>

                <TouchableOpacity
                  onPress={handleChangePhoto}
                  className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 rounded-full items-center justify-center shadow-lg"
                >
                  <Camera size={16} color="white" />
                </TouchableOpacity>
              </Box>

              <TouchableOpacity onPress={handleChangePhoto}>
                <Text className="text-blue-600 font-semibold">
                  Change Photo
                </Text>
              </TouchableOpacity>
            </VStack>
          </Box>

          {/* Personal Information */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-6 uppercase tracking-wider">
              Personal Information
            </Text>

            <VStack space="lg">
              {/* First Name */}
              <VStack space="xs">
                <HStack space="sm" className="items-center mb-2">
                  <User size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium">First Name</Text>
                </HStack>
                <Input className="border-gray-200">
                  <InputField
                    value={firstName}
                    onChangeText={setFirstName}
                    placeholder="Enter your first name"
                  />
                </Input>
              </VStack>

              {/* Last Name */}
              <VStack space="xs">
                <HStack space="sm" className="items-center mb-2">
                  <User size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium">Last Name</Text>
                </HStack>
                <Input className="border-gray-200">
                  <InputField
                    value={lastName}
                    onChangeText={setLastName}
                    placeholder="Enter your last name"
                  />
                </Input>
              </VStack>

              {/* Email */}
              <VStack space="xs">
                <HStack space="sm" className="items-center mb-2">
                  <Mail size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium">Email</Text>
                </HStack>
                <Input className="border-gray-200">
                  <InputField
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </Input>
              </VStack>

              {/* Phone Number */}
              <VStack space="xs">
                <HStack space="sm" className="items-center mb-2">
                  <Phone size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium">
                    Phone Number
                  </Text>
                </HStack>
                <Input className="border-gray-200">
                  <InputField
                    value={phoneNumber}
                    onChangeText={setPhoneNumber}
                    placeholder="Enter your phone number"
                    keyboardType="phone-pad"
                  />
                </Input>
              </VStack>
            </VStack>
          </Box>

          {/* Additional Info */}
          <Box className="bg-gray-100 rounded-xl p-4">
            <Text className="text-gray-600 text-sm text-center">
              Your profile information is used to personalize your experience
              and help others find you on the platform.
            </Text>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
