import React from 'react';
import { Text, VStack, HStack, Box, Icon } from '@/shared/ui';
import { Calendar, Clock, X } from 'lucide-react-native';
import { DeclinedMeetingProps } from '../types/message-types';

const DeclinedMeeting = React.memo((props: DeclinedMeetingProps) => {
  const { timestamp, meetingTime } = props;

  return (
    <VStack className="mb-4 self-center max-w-[85%]" space="xs">
      <Box className="rounded-2xl overflow-hidden bg-red-50 border border-red-200 shadow-sm">
        {/* Header */}
        <HStack className="bg-red-600 px-4 py-3 justify-between items-center">
          <HStack className="items-center" space="xs">
            <Icon as={Calendar} size="sm" color="#FFFFFF" />
            <Text className="text-white font-body" size="sm" bold>
              Meeting Proposal
            </Text>
          </HStack>
          <HStack className="items-center" space="xs">
            <Icon as={Clock} size="sm" color="#FFFFFF" />
            <Text className="text-white font-body" size="sm">
              {meetingTime || 'Time TBD'}
            </Text>
          </HStack>
        </HStack>

        {/* Content */}
        <Box className="px-4 py-4">
          <HStack className="items-center justify-center" space="sm">
            <Icon as={X} size="md" color="#DC2626" />
            <Text className="text-red-600 text-center font-body" size="md" bold>
              Meeting Declined
            </Text>
          </HStack>
        </Box>
      </Box>
      <Text className="text-gray-500 text-center font-body" size="xs">
        {timestamp}
      </Text>
    </VStack>
  );
});

DeclinedMeeting.displayName = 'DeclinedMeeting';

export default DeclinedMeeting;
