{"name": "@needit/db", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "scripts": {"build": "tsdown src/index.ts", "db:push": "node ../../node_modules/drizzle-kit/bin.cjs push", "db:studio": "node ../../node_modules/drizzle-kit/bin.cjs studio"}, "dependencies": {"drizzle-orm": "^0.44.2", "pg": "^8.13.1", "tslib": "^2.3.0"}, "devDependencies": {"@types/pg": "^8.11.11", "drizzle-kit": "^0.31.1", "tsdown": "^0.12.7"}}