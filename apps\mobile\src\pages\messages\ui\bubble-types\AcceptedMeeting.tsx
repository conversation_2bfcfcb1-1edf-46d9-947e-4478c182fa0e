import React from 'react';
import { MeetingQRCode } from '../meeting-qr-code';
import { AcceptedMeetingProps } from '../types/message-types';

const AcceptedMeeting = React.memo((props: AcceptedMeetingProps) => {
  const {
    timestamp,
    meetingTime,
    meetingId,
    userRole,
    createdAt, // Nouvelle prop pour la date stable
  } = props;

  // Utiliser une date fixe basée sur les données de la proposition
  const stableCreatedAt = React.useMemo(() => {
    if (createdAt) {
      return createdAt;
    }

    // Fallback: utiliser l'ID du meeting pour créer une date déterministe
    const timestampMatch = meetingId.match(/(\d+)/);
    if (timestampMatch) {
      return new Date(parseInt(timestampMatch[1])).toISOString();
    }

    // Dernier fallback: utiliser une date fixe basée sur l'ID
    const hashCode = meetingId.split('').reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    return new Date(Math.abs(hashCode) * 1000).toISOString();
  }, [meetingId, createdAt]);

  return (
    <MeetingQRCode
      meetingId={meetingId}
      meetingTime={meetingTime}
      timestamp={timestamp}
      userRole={userRole}
      createdAt={stableCreatedAt}
    />
  );
});

AcceptedMeeting.displayName = 'AcceptedMeeting';

export default AcceptedMeeting;
