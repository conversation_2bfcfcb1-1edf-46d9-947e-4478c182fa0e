import { Tabs } from 'expo-router';
import React from 'react';
import { View, Text, Platform } from 'react-native';
import {
  MapPin,
  PlusCircle,
  MessageSquare,
  User,
  Handshake,
} from 'lucide-react-native';

// Clean and professional tab icon component
const CleanTabIcon = ({
  icon: Icon,
  focused,
  color,
  size = 24,
  showBadge = false,
}: {
  icon: any;
  focused: boolean;
  color: string;
  size?: number;
  showBadge?: boolean;
}) => (
  <View
    style={{
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      marginTop: Platform.OS === 'ios' ? -4 : -2,
    }}
  >
    <Icon
      size={size}
      color={focused ? '#3B82F6' : '#9CA3AF'}
      strokeWidth={focused ? 2 : 1.5}
      fill={focused && Icon === User ? '#3B82F6' : 'transparent'}
    />
    {showBadge && (
      <View
        style={{
          position: 'absolute',
          top: -4,
          right: -4,
          width: 8,
          height: 8,
          borderRadius: 4,
          backgroundColor: '#EF4444',
        }}
      />
    )}
  </View>
);

export default function RootLayout() {
  const tabBarHeight = Platform.OS === 'ios' ? 75 : 70;

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#3B82F6', // NeedIt Blue
        tabBarInactiveTintColor: '#9CA3AF', // Light gray for inactive
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#F3F4F6',
          paddingTop: Platform.OS === 'ios' ? 10 : 8,
          paddingBottom: Platform.OS === 'ios' ? 4 : 6,
          height: tabBarHeight,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -1 },
          shadowOpacity: 0.05,
          shadowRadius: 4,
          elevation: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: Platform.OS === 'ios' ? -6 : -8,
          marginBottom: Platform.OS === 'ios' ? 1 : 3,
        },
        tabBarItemStyle: {
          paddingTop: Platform.OS === 'ios' ? 8 : 6,
          paddingBottom: Platform.OS === 'ios' ? 2 : 4,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Discover',
          tabBarIcon: ({ color, focused }) => (
            <CleanTabIcon
              icon={MapPin}
              focused={focused}
              color={color}
              size={22}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="new"
        options={{
          title: 'Ask Help',
          tabBarIcon: ({ color, focused }) => (
            <CleanTabIcon
              icon={PlusCircle}
              focused={focused}
              color={color}
              size={24}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="my-offers"
        options={{
          title: 'My Offers',
          tabBarIcon: ({ color, focused }) => (
            <CleanTabIcon
              icon={Handshake}
              focused={focused}
              color={color}
              size={22}
              showBadge={true}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, focused }) => (
            <CleanTabIcon
              icon={User}
              focused={focused}
              color={color}
              size={22}
            />
          ),
        }}
      />
      {/* Hidden screens */}
      <Tabs.Screen
        name="my-outgoing-offers"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}
