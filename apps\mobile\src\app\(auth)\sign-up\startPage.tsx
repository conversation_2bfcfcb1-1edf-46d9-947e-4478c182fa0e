import { View, Text, Image, Dimensions, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { ScrollView } from 'react-native-gesture-handler';

const slides = [
  {
    key: '1',
    title: 'NeedIt',
    description:
      'An application that connects private individuals to respond quickly, simply and efficiently to the unexpected events of everyday life.',
    image: null,
  },
  {
    key: '2',
    title: 'Fast',
    description: 'Instant service and help around you, with our application.',
    image: null,
  },
  {
    key: '3',
    title: 'Help',
    description:
      'Post your needs or find instant solutions thanks to user self-help. Wherever you are, NeedIt is there for you.',
    image: null,
  },
];

const { width } = Dimensions.get('window');

export default function StartPage() {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      router.replace('/(auth)/sign-in');
    }
  };

  const slide = slides[currentSlide];

  return (
    <View className="flex-1 bg-black items-center justify-center px-6">
      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}
        className="w-full"
      >
        <View className="flex-1 items-center justify-center">
          {slide.image && (
            <Image
              source={slide.image}
              resizeMode="contain"
              className="w-40 h-40 mb-6"
            />
          )}
          <Text className="text-white text-3xl font-bold text-center mb-4">
            {slide.title}
          </Text>
          <Text className="text-white text-base text-center">
            {slide.description}
          </Text>
        </View>

        {/* Dots */}
        <View className="flex-row justify-center mt-2 space-x-2">
          {slides.map((_, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full mx-1 ${
                index === currentSlide ? 'bg-blue-500' : 'bg-gray-600'
              }`}
            />
          ))}
        </View>

        {/* Button */}
        <TouchableOpacity
          onPress={handleNext}
          className="bg-blue-600 py-3 px-8 rounded-xl mt-8 self-center"
        >
          <Text className="text-white font-semibold">
            {currentSlide === slides.length - 1 ? "Let's go BABY" : 'Next'}
          </Text>
        </TouchableOpacity>

        {/* Languages link */}
        <Text className="text-blue-400 text-center mt-4 underline">
          Languages
        </Text>
      </ScrollView>
    </View>
  );
}
