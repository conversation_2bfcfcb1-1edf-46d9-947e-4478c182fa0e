import { messageRepository } from '@/modules/messages';
import { needRepository } from '@/modules/needs';
import type { UserSelectDto } from '@/modules/users';

import { socket } from '@/socket';

import {
  offerRepository,
  type OfferSelectDto,
  type OfferUpdateDto,
  type OfferWithMessageCreateDto,
} from '@/modules/offers';

export const offerService = {
  async createOffer(data: OfferWithMessageCreateDto) {
    const newOffer = await offerRepository.create(data);

    const offerMessage = await messageRepository.create({
      offerId: newOffer.id,
      senderId: newOffer.userId,
      content: data.message,
    });

    const need = await needRepository.findById(newOffer.needId);

    if (need) {
      socket.emit('newOffer', {
        offerId: newOffer.id,
        needId: newOffer.needId,
        needTitle: need.title,
        userId: need.userId,
      });
    }

    return {
      ...newOffer,
      message: offerMessage,
    };
  },

  async getIncomingOffers(userId: UserSelectDto['id']) {
    console.log('🔍 getIncomingOffers called for userId:', userId);
    const incomingOffers = await offerRepository.findByReceiverId(userId);
    console.log('📥 Found incoming offers:', incomingOffers.length);
    return incomingOffers;
  },

  async getOutgoingOffers(userId: UserSelectDto['id']) {
    console.log('🔍 getOutgoingOffers called for userId:', userId);
    const outgoingOffers = await offerRepository.findByUserId(userId);
    console.log('📤 Found outgoing offers:', outgoingOffers.length);
    return outgoingOffers;
  },

  async getOfferById(id: OfferSelectDto['id']) {
    const foundOffer = await offerRepository.findById(id);

    return foundOffer;
  },

  async updateOffer(id: OfferSelectDto['id'], data: Partial<OfferUpdateDto>) {
    // If the offer is being accepted, we need to reject all other offers for the same need
    if (data.status === 'accepted') {
      // First, get the offer to find its needId
      const offer = await offerRepository.findById(id);

      if (!offer) {
        throw new Error('Offer not found');
      }

      // Get all offers for this need
      const needOffers = await offerRepository.findByNeedId(offer.needId);

      // Reject all other offers for this need
      for (const needOffer of needOffers) {
        if (needOffer.id !== id) {
          await offerRepository.updateById(needOffer.id, {
            status: 'rejected',
          });
        }
      }
    }

    // Update the requested offer
    const updatedOffer = await offerRepository.updateById(id, data);

    return updatedOffer;
  },
};
