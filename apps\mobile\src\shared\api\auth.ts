import { User } from '@/shared/model';

import { nativeFetch } from '@/shared/lib';

type Session = {
  id: string;
  userId: User['id'];
  userAgent: string;
  ipAdress: string;
  createdAt: string;
  updatedAt: string;
  user: User;
};

export async function getSession() {
  const response = await nativeFetch('/api/auth/session', {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    return null;
  }

  const data = await response.json();
  return data.session as Session;
}

export async function signOut() {
  // Make the API call to sign out on the server
  const response = await nativeFetch('/api/auth/sign-out', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });
  
  // Import and use the queryClient to invalidate the auth query
  const { queryClient } = await import('@/app/_providers/react-query-provider');
  
  // Invalidate the auth query to update the UI
  queryClient.invalidateQueries({ queryKey: ['auth'] });
  queryClient.clear(); // Clear all queries from the cache
  
  return response;
}
