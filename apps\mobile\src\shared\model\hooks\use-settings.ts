import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import {
  getUserSettings,
  updateSettings,
  updateNotificationSettings,
  updatePrivacySettings,
  updateAppearanceSettings,
  resetSettingsToDefaults,
  deleteUserSettings,
  type UserSettings,
  type SettingsUpdate,
  type NotificationSettings,
  type PrivacySettings,
  type AppearanceSettings,
} from '@/shared/api';

const SETTINGS_QUERY_KEY = ['settings'];

/**
 * Hook to get user settings
 */
export function useSettings() {
  const {
    data: settings,
    error,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: SETTINGS_QUERY_KEY,
    queryFn: getUserSettings,
  });

  return {
    settings,
    error,
    isLoading,
    refetch,
  };
}

/**
 * Hook to update general settings
 */
export function useUpdateSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: SettingsUpdate) => updateSettings(settings),
    onSuccess: (data) => {
      queryClient.setQueryData(SETTINGS_QUERY_KEY, data);
      console.log('Settings updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update settings:', error.message);
    },
  });
}

/**
 * Hook to update notification settings
 */
export function useUpdateNotificationSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: NotificationSettings) =>
      updateNotificationSettings(settings),
    onSuccess: (data) => {
      queryClient.setQueryData(SETTINGS_QUERY_KEY, data);
      console.log('Notification settings updated');
    },
    onError: (error) => {
      console.error('Failed to update notification settings:', error.message);
    },
  });
}

/**
 * Hook to update privacy settings
 */
export function useUpdatePrivacySettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: PrivacySettings) => updatePrivacySettings(settings),
    onSuccess: (data) => {
      queryClient.setQueryData(SETTINGS_QUERY_KEY, data);
      console.log('Privacy settings updated');
    },
    onError: (error) => {
      console.error('Failed to update privacy settings:', error.message);
    },
  });
}

/**
 * Hook to update appearance settings
 */
export function useUpdateAppearanceSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: AppearanceSettings) =>
      updateAppearanceSettings(settings),
    onSuccess: (data) => {
      queryClient.setQueryData(SETTINGS_QUERY_KEY, data);
      console.log('Appearance settings updated');
    },
    onError: (error) => {
      console.error('Failed to update appearance settings:', error.message);
    },
  });
}

/**
 * Hook to reset settings to defaults
 */
export function useResetSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => resetSettingsToDefaults(),
    onSuccess: (data) => {
      queryClient.setQueryData(SETTINGS_QUERY_KEY, data);
      console.log('Settings reset to defaults');
    },
    onError: (error) => {
      console.error('Failed to reset settings:', error.message);
    },
  });
}

/**
 * Hook to delete user settings
 */
export function useDeleteSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => deleteUserSettings(),
    onSuccess: () => {
      queryClient.removeQueries({ queryKey: SETTINGS_QUERY_KEY });
      console.log('Settings deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete settings:', error.message);
    },
  });
}
