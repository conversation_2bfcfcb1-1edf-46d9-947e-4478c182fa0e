import { useRouter } from 'expo-router';
import { Text, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { useState, useEffect } from 'react';

import {
  ArrowLeft,
  Bell,
  Mail,
  Smartphone,
  MessageCircle,
  DollarSign,
  Calendar,
  Clock,
  Shield,
} from 'lucide-react-native';

import {
  useSettings,
  useUpdateNotificationSettings,
} from '@/shared/model/hooks/use-settings';

import { Box, VStack, HStack, Heading, SafeAreaView } from '@/shared/ui';

export default function NotificationsPage() {
  const router = useRouter();
  const { settings, isLoading } = useSettings();
  const updateNotificationSettings = useUpdateNotificationSettings();

  // Local state for switches
  const [pushNotifications, setPushNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);

  // Category-specific notifications
  const [messageNotifications, setMessageNotifications] = useState(true);
  const [offerNotifications, setOfferNotifications] = useState(true);
  const [paymentNotifications, setPaymentNotifications] = useState(true);
  const [reminderNotifications, setReminderNotifications] = useState(true);
  const [securityNotifications, setSecurityNotifications] = useState(true);

  // Sync with backend settings
  useEffect(() => {
    if (settings) {
      setPushNotifications(settings.pushNotifications);
      setEmailNotifications(settings.emailNotifications);
      setMarketingEmails(settings.marketingEmails);
    }
  }, [settings]);

  const handleSaveSettings = () => {
    updateNotificationSettings.mutate({
      notificationsEnabled: pushNotifications || emailNotifications,
      pushNotifications,
      emailNotifications,
      marketingEmails,
    });
  };

  const NotificationToggle = ({
    icon: IconComponent,
    title,
    subtitle,
    value,
    onValueChange,
    iconColor = '#6B7280',
    iconBgColor = '#F3F4F6',
  }: {
    icon: any;
    title: string;
    subtitle: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    iconColor?: string;
    iconBgColor?: string;
  }) => (
    <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
      <HStack space="md" className="items-center">
        <Box
          className="w-10 h-10 rounded-xl items-center justify-center"
          style={{ backgroundColor: iconBgColor }}
        >
          <IconComponent size={20} color={iconColor} />
        </Box>

        <VStack className="flex-1" space="xs">
          <Text className="text-gray-900 font-semibold text-base">{title}</Text>
          <Text className="text-gray-500 text-sm">{subtitle}</Text>
        </VStack>

        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#F3F4F6', true: '#3B82F6' }}
          thumbColor={value ? '#FFFFFF' : '#9CA3AF'}
        />
      </HStack>
    </Box>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Notifications
            </Heading>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* General Settings */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              General Settings
            </Text>

            <NotificationToggle
              icon={Smartphone}
              title="Push Notifications"
              subtitle="Receive notifications on your device"
              value={pushNotifications}
              onValueChange={(value) => {
                setPushNotifications(value);
                handleSaveSettings();
              }}
              iconColor="#3B82F6"
              iconBgColor="#EFF6FF"
            />

            <NotificationToggle
              icon={Mail}
              title="Email Notifications"
              subtitle="Receive notifications via email"
              value={emailNotifications}
              onValueChange={(value) => {
                setEmailNotifications(value);
                handleSaveSettings();
              }}
              iconColor="#10B981"
              iconBgColor="#F0FDF4"
            />

            <NotificationToggle
              icon={Bell}
              title="Marketing Emails"
              subtitle="Receive updates about new features and offers"
              value={marketingEmails}
              onValueChange={(value) => {
                setMarketingEmails(value);
                handleSaveSettings();
              }}
              iconColor="#F59E0B"
              iconBgColor="#FFFBEB"
            />
          </VStack>

          {/* Category-specific Notifications */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Notification Categories
            </Text>

            <NotificationToggle
              icon={MessageCircle}
              title="Messages"
              subtitle="New messages and chat notifications"
              value={messageNotifications}
              onValueChange={setMessageNotifications}
              iconColor="#8B5CF6"
              iconBgColor="#F5F3FF"
            />

            <NotificationToggle
              icon={DollarSign}
              title="Offers & Payments"
              subtitle="New offers, payment confirmations"
              value={offerNotifications}
              onValueChange={setOfferNotifications}
              iconColor="#10B981"
              iconBgColor="#F0FDF4"
            />

            <NotificationToggle
              icon={Calendar}
              title="Payment Updates"
              subtitle="Payment receipts and transaction alerts"
              value={paymentNotifications}
              onValueChange={setPaymentNotifications}
              iconColor="#06B6D4"
              iconBgColor="#F0F9FF"
            />

            <NotificationToggle
              icon={Clock}
              title="Reminders"
              subtitle="Appointment reminders and deadlines"
              value={reminderNotifications}
              onValueChange={setReminderNotifications}
              iconColor="#F59E0B"
              iconBgColor="#FFFBEB"
            />

            <NotificationToggle
              icon={Shield}
              title="Security Alerts"
              subtitle="Login alerts and security notifications"
              value={securityNotifications}
              onValueChange={setSecurityNotifications}
              iconColor="#EF4444"
              iconBgColor="#FEF2F2"
            />
          </VStack>

          {/* Quiet Hours */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Quiet Hours
            </Text>

            <VStack space="md">
              <HStack space="md" className="items-center justify-between">
                <VStack className="flex-1">
                  <Text className="text-gray-900 font-semibold">
                    Do Not Disturb
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    Silence notifications during specific hours
                  </Text>
                </VStack>
                <Switch
                  value={false}
                  onValueChange={() => console.log('Do not disturb toggle')}
                  trackColor={{ false: '#F3F4F6', true: '#3B82F6' }}
                  thumbColor={'#9CA3AF'}
                />
              </HStack>

              <Box className="bg-gray-50 rounded-lg p-4">
                <Text className="text-gray-600 text-sm text-center">
                  📵 Configure quiet hours: 10:00 PM - 8:00 AM
                </Text>
              </Box>
            </VStack>
          </Box>

          {/* Notification Preview */}
          <Box className="bg-blue-50 rounded-xl p-4">
            <VStack space="sm">
              <Text className="text-blue-900 font-semibold">📱 Preview</Text>
              <Text className="text-blue-800 text-sm">
                You'll receive notifications based on your preferences above.
                You can change these settings anytime.
              </Text>
            </VStack>
          </Box>

          {/* Tips */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Tips
            </Text>

            <VStack space="sm">
              <Text className="text-gray-600 text-sm">
                • Enable security alerts to stay informed about account activity
              </Text>
              <Text className="text-gray-600 text-sm">
                • Turn off marketing emails if you prefer minimal notifications
              </Text>
              <Text className="text-gray-600 text-sm">
                • Use quiet hours to avoid interruptions during sleep
              </Text>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
