import { createClient } from '@supabase/supabase-js';

import { env } from '@/shared/config';
import { db, schema } from '@needit/db';

// Initialize Supabase client
const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_ANON_KEY);

export const fileRepository = {
  // async uploadFile(file: File, uploaderId: string) {
  //   // Generate a unique file path, prepending 'temp/' if userId is empty
  //   const filePath = uploaderId
  //     ? `${uploaderId}/${Date.now()}_${file.name}`
  //     : `temp/${Date.now()}_${file.name}`;

  //   // Upload file to Supabase Storage
  //   const { data, error } = await supabase.storage
  //     .from('user-uploads')
  //     .upload(filePath, file, {
  //       contentType: file.type,
  //       upsert: false,
  //     });

  //   if (error) {
  //     throw new Error(`Failed to upload file: ${error.message}`);
  //   }

  //   // Get public URL
  //   const {
  //     data: { publicUrl },
  //   } = supabase.storage.from('user-uploads').getPublicUrl(filePath);

  //   return {
  //     imageRef: filePath,
  //     url: publicUrl,
  //   };
  // },
  async storeFile(file: File, bucket: string) {
    const filePath = `${Date.now()}_${file.name}`;

    const { data: _data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        contentType: file.type,
        upsert: false,
      });

    if (error) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(filePath);

    const dbFile = await db
      .insert(schema.files)
      .values({
        bucket,
        path: filePath,
        type: file.type,
        size: file.size,
      })
      .returning()
      .catch((error) => {
        throw new Error(`Failed to insert file in DB: ${error.message}`);
      })
      .then((res) => res[0]);

    return dbFile;
  },

  async getMediaUrl(imageRef: string) {
    const {
      data: { publicUrl },
    } = supabase.storage.from('user-uploads').getPublicUrl(imageRef);

    return publicUrl;
  },

  async deleteMedia(imageRef: string) {
    const { error } = await supabase.storage
      .from('user-uploads')
      .remove([imageRef]);

    if (error) {
      throw new Error(`Failed to delete file: ${error.message}`);
    }

    return true;
  },

  async renameMedia(oldPath: string, newPath: string) {
    const { error } = await supabase.storage
      .from('user-uploads')
      .move(oldPath, newPath);

    if (error) {
      throw new Error(`Failed to rename file: ${error.message}`);
    }
  },
};
