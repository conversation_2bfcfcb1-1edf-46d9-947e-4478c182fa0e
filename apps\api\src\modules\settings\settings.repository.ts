import { eq } from 'drizzle-orm';

import { db } from '@needit/db';
import { schema } from '@needit/db';

import type {
  SettingsCreateDto,
  SettingsSelectDto,
  SettingsUpdateDto,
} from './settings.dto';

export class SettingsRepository {
  /**
   * Get user settings by user ID
   */
  async findByUserId(userId: string): Promise<SettingsSelectDto | null> {
    const settings = await db
      .select()
      .from(schema.userSettings)
      .where(eq(schema.userSettings.userId, userId))
      .limit(1);

    return settings[0] || null;
  }

  /**
   * Create default settings for a user
   */
  async create(data: SettingsCreateDto): Promise<SettingsSelectDto> {
    const [settings] = await db
      .insert(schema.userSettings)
      .values(data)
      .returning();

    return settings;
  }

  /**
   * Update user settings
   */
  async update(
    userId: string,
    data: SettingsUpdateDto
  ): Promise<SettingsSelectDto | null> {
    const [settings] = await db
      .update(schema.userSettings)
      .set(data)
      .where(eq(schema.userSettings.userId, userId))
      .returning();

    return settings || null;
  }

  /**
   * Delete user settings
   */
  async delete(userId: string): Promise<void> {
    await db
      .delete(schema.userSettings)
      .where(eq(schema.userSettings.userId, userId));
  }

  /**
   * Get or create user settings (if they don't exist)
   */
  async getOrCreate(userId: string): Promise<SettingsSelectDto> {
    let settings = await this.findByUserId(userId);

    if (!settings) {
      settings = await this.create({ userId });
    }

    return settings;
  }
}
