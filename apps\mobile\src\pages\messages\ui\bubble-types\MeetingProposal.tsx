import React, { useCallback } from 'react';
import { TouchableOpacity } from 'react-native';
import { Text, VStack, HStack, Box, Icon } from '@/shared/ui';
import { Calendar, Clock, Check, X } from 'lucide-react-native';
import { MeetingProposalProps } from '../types/message-types';

const MeetingProposal = React.memo((props: MeetingProposalProps) => {
  const { id, sender, timestamp, meetingTime, onAccept, onDecline } = props;

  const isUser = sender === 'user';

  const handleAccept = useCallback(() => {
    if (onAccept && id) {
      onAccept(id);
    }
  }, [onAccept, id]);

  const handleDecline = useCallback(() => {
    if (onDecline && id) {
      onDecline(id);
    }
  }, [onDecline, id]);

  // Handle meeting proposal from current user
  if (isUser) {
    return (
      <VStack className="mb-4 self-end max-w-[85%]" space="xs">
        <Box className="rounded-2xl overflow-hidden bg-blue-50 border border-blue-200 shadow-sm">
          {/* Header */}
          <HStack className="bg-blue-600 px-4 py-3 justify-between items-center">
            <HStack className="items-center" space="xs">
              <Icon as={Calendar} size="sm" color="#FFFFFF" />
              <Text className="text-white font-body" size="sm" bold>
                Meeting Proposal
              </Text>
            </HStack>
            <HStack className="items-center" space="xs">
              <Icon as={Clock} size="sm" color="#FFFFFF" />
              <Text className="text-white font-body" size="sm">
                {meetingTime || 'Time TBD'}
              </Text>
            </HStack>
          </HStack>

          {/* Content */}
          <Box className="px-4 py-4">
            <Text
              className="text-blue-600 text-center font-body"
              size="md"
              bold
            >
              Waiting for response...
            </Text>
          </Box>
        </Box>
        <Text className="text-gray-500 text-right mr-2 font-body" size="xs">
          {timestamp}
        </Text>
      </VStack>
    );
  }

  // Handle meeting proposal from other user
  return (
    <VStack className="mb-4 self-center max-w-[85%]" space="xs">
      <Box className="rounded-2xl overflow-hidden bg-blue-50 border border-blue-200 shadow-sm">
        {/* Header */}
        <HStack className="bg-blue-600 px-4 py-3 justify-between items-center">
          <HStack className="items-center" space="xs">
            <Icon as={Calendar} size="sm" color="#FFFFFF" />
            <Text className="text-white font-body" size="sm" bold>
              Meeting Proposal
            </Text>
          </HStack>
          <HStack className="items-center" space="xs">
            <Icon as={Clock} size="sm" color="#FFFFFF" />
            <Text className="text-white font-body" size="sm">
              {meetingTime || 'Time TBD'}
            </Text>
          </HStack>
        </HStack>

        {/* Content */}
        <Box className="px-4 py-4">
          <Text
            className="text-blue-600 text-center font-body mb-4"
            size="md"
            bold
          >
            Would you like to meet?
          </Text>

          {/* Action buttons */}
          <HStack className="border-t border-blue-200 -mx-4 -mb-4">
            <TouchableOpacity
              className="flex-1 py-4 items-center bg-green-50 border-r border-blue-200"
              onPress={handleAccept}
            >
              <HStack className="items-center" space="xs">
                <Icon as={Check} size="sm" color="#059669" />
                <Text className="text-green-600 font-body" size="sm" bold>
                  Accept
                </Text>
              </HStack>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-1 py-4 items-center bg-red-50"
              onPress={handleDecline}
            >
              <HStack className="items-center" space="xs">
                <Icon as={X} size="sm" color="#DC2626" />
                <Text className="text-red-600 font-body" size="sm" bold>
                  Decline
                </Text>
              </HStack>
            </TouchableOpacity>
          </HStack>
        </Box>
      </Box>
      <Text className="text-gray-500 text-center font-body" size="xs">
        {timestamp}
      </Text>
    </VStack>
  );
});

MeetingProposal.displayName = 'MeetingProposal';

export default MeetingProposal;
