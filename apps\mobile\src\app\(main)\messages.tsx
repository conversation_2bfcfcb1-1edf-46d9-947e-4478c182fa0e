import React, { useCallback, useMemo } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import { VStack, Text, Icon, ArrowLeftIcon, Button } from '@/shared/ui';
import { ScrollView, View } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import {
  ChatHeader,
  OfferInfoHeader,
  MeetingQRCode,
  MessageBubble,
  MessageInput,
} from '@/pages/messages';
import { useOfferMessages, useMeetingProposalOperations } from '@/shared/model';

export default function Messages() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const offerId = (params.id as string) || '1';

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  // Utiliser les vraies APIs
  const {
    messages,
    isLoadingMessages,
    inputMessage,
    setInputMessage,
    handleSendMessage,
    fetchMessages,
  } = useOfferMessages({ offerId });

  const {
    proposals,
    isLoading: isLoadingProposals,
    createProposal,
    acceptProposal,
    declineProposal,
    isCreating,
  } = useMeetingProposalOperations(offerId);

  // TODO: Replace with real data from API
  const [offer] = React.useState({
    id: offerId,
    title: 'Need Help',
    description: 'Loading...',
    price: '0',
    status: 'Available',
    location: 'Paris',
  });

  const [otherUser] = React.useState({
    firstName: 'User',
    lastName: '',
    imageUrl: '',
    isOnline: false,
  });

  const scrollViewRef = React.useRef<ScrollView>(null);

  React.useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: false });
  }, [messages, proposals]);

  // Fonction pour créer une proposition de meeting
  const handleMakeOffer = useCallback(() => {
    try {
      const currentTime = new Date();
      const meetingTime = new Date(currentTime.getTime() + 60 * 60 * 1000);

      // TODO: Obtenir le receiverId depuis l'offre ou les données utilisateur
      const receiverId = 'temp-receiver-id'; // À remplacer par la vraie logique

      createProposal({
        offerId,
        receiverId,
        proposedMeetingTime: meetingTime.toISOString(),
        message: 'I would like to propose a meeting.',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24h
      });

      setTimeout(() => {
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({ animated: true });
        }
      }, 100);
    } catch (error) {
      console.error('Error in handleMakeOffer:', error);
      Alert.alert(
        'Error',
        'An error occurred while creating the meeting proposal.'
      );
    }
  }, [offerId, createProposal]);

  const handleSend = useCallback(() => {
    if (!inputMessage.trim()) return;

    handleSendMessage();
  }, [inputMessage, handleSendMessage]);

  const handleAcceptMeeting = useCallback(
    (proposalId: string) => {
      acceptProposal(proposalId);
    },
    [acceptProposal]
  );

  const handleDeclineMeeting = useCallback(
    (proposalId: string) => {
      declineProposal(proposalId);
    },
    [declineProposal]
  );

  // Combiner les messages et propositions avec mémorisation
  const combinedMessages = useMemo(() => {
    const combined: any[] = [];

    // Ajouter les messages réguliers
    messages.forEach((msg) => {
      combined.push({
        id: msg.id,
        text: msg.content,
        sender: msg.senderId === 'current-user-id' ? 'user' : 'other', // TODO: remplacer par le vrai user ID
        timestamp: new Date(msg.createdAt).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        type: 'regular',
        createdAt: msg.createdAt,
      });
    });

    // Ajouter les propositions de meeting
    proposals.forEach((proposal) => {
      const meetingTime = new Date(
        proposal.proposedMeetingTime
      ).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      let messageType: 'proposal' | 'accepted' | 'declined' = 'proposal';
      if (proposal.status === 'accepted') {
        messageType = 'accepted';
      } else if (proposal.status === 'declined') {
        messageType = 'declined';
      }

      combined.push({
        id: proposal.id,
        sender: proposal.proposerId === 'current-user-id' ? 'user' : 'other', // TODO: remplacer par le vrai user ID
        timestamp: new Date(proposal.createdAt).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        type: messageType,
        meetingTime,
        meetingId: proposal.id,
        userRole:
          proposal.proposerId === 'current-user-id'
            ? 'offer_creator'
            : 'need_creator', // TODO: remplacer par la vraie logique
        proposal, // Ajouter l'objet proposal complet pour accès aux données QR
        createdAt: proposal.createdAt,
      });
    });

    // Trier par date de création
    return combined.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }, [messages, proposals]);

  const renderMessage = useCallback(
    (msg: any) => {
      const baseProps = {
        id: msg.id,
        sender: msg.sender,
        timestamp: msg.timestamp,
        type: msg.type,
      };

      let messageProps;

      switch (msg.type) {
        case 'regular':
          messageProps = {
            ...baseProps,
            text: msg.text,
          };
          break;
        case 'proposal':
          messageProps = {
            ...baseProps,
            meetingTime: msg.meetingTime,
            onAccept: handleAcceptMeeting,
            onDecline: handleDeclineMeeting,
          };
          break;
        case 'accepted':
          messageProps = {
            ...baseProps,
            meetingTime: msg.meetingTime,
            meetingId: msg.meetingId,
            userRole: msg.userRole,
            // Utiliser les données QR stables de la proposition
            createdAt: msg.proposal?.qrCodeData
              ? JSON.parse(msg.proposal.qrCodeData).acceptedAt
              : msg.proposal?.acceptedAt,
          };
          break;
        case 'declined':
          messageProps = {
            ...baseProps,
            meetingTime: msg.meetingTime,
          };
          break;
      }

      return <MessageBubble key={msg.id} {...messageProps} />;
    },
    [handleAcceptMeeting, handleDeclineMeeting]
  );

  return (
    <View style={{ flex: 1 }} className="bg-gray-50">
      <ChatHeader
        firstName={otherUser.firstName}
        lastName={otherUser.lastName}
        imageUrl={otherUser.imageUrl}
        isOnline={otherUser.isOnline}
        onBack={handleBack}
      />

      {offer && (
        <OfferInfoHeader
          title={offer.title}
          description={offer.description}
          price={offer.price}
          status={offer.status}
          location={offer.location}
        />
      )}

      {/* Messages list */}
      <ScrollView
        ref={scrollViewRef}
        className="flex-1 px-4"
        contentContainerStyle={{
          paddingTop: 20,
          paddingBottom: 120,
        }}
      >
        {isLoadingMessages || isLoadingProposals ? (
          <View className="flex-1 justify-center items-center py-16">
            <Text className="text-gray-500 text-center">
              Loading messages...
            </Text>
          </View>
        ) : combinedMessages.length === 0 ? (
          <View className="flex-1 justify-center items-center py-16">
            <Text className="text-gray-500 text-center">
              No messages yet. Start the conversation!
            </Text>
          </View>
        ) : (
          combinedMessages.map(renderMessage)
        )}
      </ScrollView>

      {/* Message input - positioned at bottom */}
      <View style={{ position: 'absolute', bottom: 0, left: 0, right: 0 }}>
        <MessageInput
          message={inputMessage}
          setMessage={setInputMessage}
          onSend={handleSend}
          onMakeOffer={handleMakeOffer}
          showMakeOfferButton={!isCreating}
        />
      </View>
    </View>
  );
}
