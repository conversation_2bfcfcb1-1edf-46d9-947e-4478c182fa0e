import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  HStack,
  Text,
  VStack,
  Icon,
  Box,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from '@/shared/ui';
import { ArrowLeft } from 'lucide-react-native';

interface ChatHeaderProps {
  firstName: string;
  lastName: string;
  imageUrl: string;
  isOnline: boolean;
  onBack?: () => void;
}

export function ChatHeader({
  firstName,
  lastName,
  imageUrl,
  isOnline,
  onBack,
}: ChatHeaderProps) {
  return (
    <Box className="bg-white pt-20 pb-6 border-b border-gray-100 shadow-sm">
      <Box className="px-6">
        {/* Header with back button */}
        <HStack className="justify-between items-center mb-6">
          {onBack && (
            <TouchableOpacity
              onPress={onBack}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center shadow-sm"
            >
              <Icon as={ArrowLeft} size="md" color="#6B7280" />
            </TouchableOpacity>
          )}
          <Box className="w-10 h-10" />
        </HStack>

        {/* User Info Card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <HStack className="items-center" space="md">
            {/* Profile Picture with Online Status */}
            <Box className="relative">
              <Avatar size="lg" className="w-12 h-12">
                <AvatarImage
                  source={{
                    uri: imageUrl,
                  }}
                />
                <AvatarFallbackText>
                  {firstName[0]}
                  {lastName[0]}
                </AvatarFallbackText>
              </Avatar>
              {/* Online Status Indicator */}
              <Box
                className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                  isOnline ? 'bg-green-500' : 'bg-gray-400'
                }`}
              />
            </Box>

            {/* User Info */}
            <VStack className="flex-1" space="xs">
              <Text className="text-gray-900 font-body" size="lg" bold>
                {firstName} {lastName}
              </Text>
              <Text className="text-gray-500 font-body" size="sm">
                {isOnline ? 'Active now' : 'Last seen recently'}
              </Text>
            </VStack>
          </HStack>
        </Box>
      </Box>
    </Box>
  );
}
