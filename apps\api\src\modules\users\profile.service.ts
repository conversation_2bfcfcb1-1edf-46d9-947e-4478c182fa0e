import { userRepository } from './user.repository';
import { profileRepository } from './profile.repository';
import type { UserSelectDto, UserUpdateDto } from './user.dto';

export const profileService = {
  /**
   * Get the full profile of a user by ID
   */
  async getProfile(userId: UserSelectDto['id']) {
    const user = await userRepository.findById(userId);
    
    if (!user) {
      return null;
    }
    
    return user;
  },
  
  /**
   * Update a user's profile
   */
  async updateProfile(userId: UserSelectDto['id'], data: UserUpdateDto) {
    return await userRepository.updateById(userId, data);
  },
  
  /**
   * Update a user's profile picture
   */
  async updateProfilePicture(userId: UserSelectDto['id'], imageRef: string) {
    return await userRepository.updateById(userId, { imageRef });
  },
  
  /**
   * Get user statistics (needs, offers, etc.)
   */
  async getProfileStats(userId: UserSelectDto['id']) {
    return await profileRepository.getUserStats(userId);
  },
  
  /**
   * Get user's recent needs
   */
  async getRecentNeeds(userId: UserSelectDto['id'], limit = 5) {
    return await profileRepository.getRecentNeeds(userId, limit);
  },
  
  /**
   * Get user's recent offers
   */
  async getRecentOffers(userId: UserSelectDto['id'], limit = 5) {
    return await profileRepository.getRecentOffers(userId, limit);
  },
  
  /**
   * Get user's recent messages
   */
  async getRecentMessages(userId: UserSelectDto['id'], limit = 5) {
    return await profileRepository.getRecentMessages(userId, limit);
  },
  
  /**
   * Get user's activity timeline
   */
  async getActivityTimeline(userId: UserSelectDto['id'], limit = 10) {
    return await profileRepository.getActivityTimeline(userId, limit);
  }
};
