import { useRouter } from 'expo-router';
import { Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useState } from 'react';

import {
  ArrowLeft,
  CreditCard,
  Plus,
  Trash2,
  Check,
  Apple,
  Smartphone,
} from 'lucide-react-native';

import { Box, VStack, HStack, Heading, SafeAreaView } from '@/shared/ui';

type PaymentMethod = {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  email?: string;
};

export default function PaymentMethods() {
  const router = useRouter();

  // Mock data - replace with actual API data
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true,
    },
    {
      id: '2',
      type: 'paypal',
      email: '<EMAIL>',
      isDefault: false,
    },
    {
      id: '3',
      type: 'apple_pay',
      isDefault: false,
    },
  ]);

  const handleSetDefault = (id: string) => {
    setPaymentMethods((methods) =>
      methods.map((method) => ({
        ...method,
        isDefault: method.id === id,
      }))
    );
  };

  const handleDelete = (id: string, type: string) => {
    Alert.alert(
      'Delete Payment Method',
      `Are you sure you want to remove this ${type}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setPaymentMethods((methods) =>
              methods.filter((method) => method.id !== id)
            );
          },
        },
      ]
    );
  };

  const handleAddPaymentMethod = () => {
    Alert.alert('Add Payment Method', 'Choose a payment method to add', [
      { text: 'Credit Card', onPress: () => console.log('Add card') },
      { text: 'PayPal', onPress: () => console.log('Add PayPal') },
      { text: 'Apple Pay', onPress: () => console.log('Add Apple Pay') },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const PaymentMethodItem = ({ method }: { method: PaymentMethod }) => {
    const getIcon = () => {
      switch (method.type) {
        case 'card':
          return <CreditCard size={20} color="#6B7280" />;
        case 'paypal':
          return <CreditCard size={20} color="#0070BA" />;
        case 'apple_pay':
          return <Apple size={20} color="#000000" />;
        case 'google_pay':
          return <Smartphone size={20} color="#4285F4" />;
        default:
          return <CreditCard size={20} color="#6B7280" />;
      }
    };

    const getTitle = () => {
      switch (method.type) {
        case 'card':
          return `${method.brand} •••• ${method.last4}`;
        case 'paypal':
          return 'PayPal';
        case 'apple_pay':
          return 'Apple Pay';
        case 'google_pay':
          return 'Google Pay';
        default:
          return 'Unknown';
      }
    };

    const getSubtitle = () => {
      switch (method.type) {
        case 'card':
          return `Expires ${method.expiryMonth}/${method.expiryYear}`;
        case 'paypal':
          return method.email;
        case 'apple_pay':
          return 'Touch ID or Face ID';
        case 'google_pay':
          return 'Tap to pay';
        default:
          return '';
      }
    };

    return (
      <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
        <HStack space="md" className="items-center">
          <Box className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center">
            {getIcon()}
          </Box>

          <VStack className="flex-1" space="xs">
            <HStack space="sm" className="items-center">
              <Text className="text-gray-900 font-semibold text-base">
                {getTitle()}
              </Text>
              {method.isDefault && (
                <Box className="bg-green-100 px-2 py-1 rounded-md">
                  <Text className="text-green-700 text-xs font-medium">
                    Default
                  </Text>
                </Box>
              )}
            </HStack>
            <Text className="text-gray-500 text-sm">{getSubtitle()}</Text>
          </VStack>

          <HStack space="sm">
            {!method.isDefault && (
              <TouchableOpacity
                onPress={() => handleSetDefault(method.id)}
                className="w-8 h-8 bg-blue-100 rounded-lg items-center justify-center"
              >
                <Check size={16} color="#3B82F6" />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              onPress={() => handleDelete(method.id, method.type)}
              className="w-8 h-8 bg-red-100 rounded-lg items-center justify-center"
            >
              <Trash2 size={16} color="#EF4444" />
            </TouchableOpacity>
          </HStack>
        </HStack>
      </Box>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Payment Methods
            </Heading>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Add Payment Method */}
          <TouchableOpacity
            onPress={handleAddPaymentMethod}
            className="bg-white rounded-xl shadow-sm border border-gray-100 p-4"
          >
            <HStack space="md" className="items-center">
              <Box className="w-10 h-10 bg-blue-100 rounded-xl items-center justify-center">
                <Plus size={20} color="#3B82F6" />
              </Box>
              <VStack className="flex-1">
                <Text className="text-gray-900 font-semibold text-base">
                  Add Payment Method
                </Text>
                <Text className="text-gray-500 text-sm">
                  Credit card, PayPal, or digital wallet
                </Text>
              </VStack>
            </HStack>
          </TouchableOpacity>

          {/* Current Payment Methods */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Your Payment Methods
            </Text>

            {paymentMethods.map((method) => (
              <PaymentMethodItem key={method.id} method={method} />
            ))}
          </VStack>

          {/* Security Info */}
          <Box className="bg-blue-50 rounded-xl p-4">
            <VStack space="sm">
              <Text className="text-blue-900 font-semibold">
                🔒 Secure Payments
              </Text>
              <Text className="text-blue-800 text-sm">
                Your payment information is encrypted and securely stored. We
                never store your full card details.
              </Text>
            </VStack>
          </Box>

          {/* Supported Methods */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Supported Payment Methods
            </Text>

            <VStack space="sm">
              <HStack space="sm" className="items-center">
                <CreditCard size={16} color="#6B7280" />
                <Text className="text-gray-700">
                  Credit & Debit Cards (Visa, Mastercard, Amex)
                </Text>
              </HStack>
              <HStack space="sm" className="items-center">
                <CreditCard size={16} color="#0070BA" />
                <Text className="text-gray-700">PayPal</Text>
              </HStack>
              <HStack space="sm" className="items-center">
                <Apple size={16} color="#000000" />
                <Text className="text-gray-700">Apple Pay</Text>
              </HStack>
              <HStack space="sm" className="items-center">
                <Smartphone size={16} color="#4285F4" />
                <Text className="text-gray-700">Google Pay</Text>
              </HStack>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
