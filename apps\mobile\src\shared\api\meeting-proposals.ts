import { nativeFetch } from '@/shared/lib';

export interface MeetingProposal {
  id: string;
  offerId: string;
  proposerId: string;
  receiverId: string;
  proposedMeetingTime: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  qrCodeData?: string;
  acceptedAt?: string;
  declinedAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt?: string;
  proposer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  receiver?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateMeetingProposalDto {
  offerId: string;
  receiverId: string;
  proposedMeetingTime: string;
  message?: string;
  expiresAt?: string;
}

// Créer une proposition de meeting
export async function createMeetingProposal(
  data: CreateMeetingProposalDto
): Promise<MeetingProposal> {
  const response = await nativeFetch('/api/meeting-proposals', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to create meeting proposal');
  }

  const result = await response.json();
  return result.proposal;
}

// Obtenir les propositions pour une offre
export async function getMeetingProposalsByOfferId(
  offerId: string
): Promise<MeetingProposal[]> {
  try {
    const response = await nativeFetch(
      `/api/meeting-proposals/offer/${offerId}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );

    if (!response.ok) {
      console.error(
        'Failed to fetch meeting proposals, status:',
        response.status
      );
      return [];
    }

    const data = await response.json();
    return data.proposals as MeetingProposal[];
  } catch (error) {
    console.error('Error fetching meeting proposals:', error);
    return [];
  }
}

// Obtenir une proposition par ID
export async function getMeetingProposalById(
  proposalId: string
): Promise<MeetingProposal | null> {
  try {
    const response = await nativeFetch(`/api/meeting-proposals/${proposalId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to fetch meeting proposal');
    }

    const data = await response.json();
    return data.proposal;
  } catch (error) {
    console.error('Error fetching meeting proposal:', error);
    return null;
  }
}

// Accepter une proposition
export async function acceptMeetingProposal(
  proposalId: string
): Promise<MeetingProposal> {
  const response = await nativeFetch(
    `/api/meeting-proposals/${proposalId}/accept`,
    {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to accept meeting proposal');
  }

  const result = await response.json();
  return result.proposal;
}

// Décliner une proposition
export async function declineMeetingProposal(
  proposalId: string
): Promise<MeetingProposal> {
  const response = await nativeFetch(
    `/api/meeting-proposals/${proposalId}/decline`,
    {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to decline meeting proposal');
  }

  const result = await response.json();
  return result.proposal;
}
