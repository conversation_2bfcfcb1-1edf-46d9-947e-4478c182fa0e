import { useRouter } from 'expo-router';
import {
  Text,
  TouchableOpacity,
  ScrollView,
  Linking,
  Alert,
} from 'react-native';
import { useState } from 'react';

import {
  ArrowLeft,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone,
  FileText,
  Bug,
  Star,
  ExternalLink,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Send,
} from 'lucide-react-native';

import {
  Box,
  VStack,
  HStack,
  Heading,
  SafeAreaView,
  Input,
  InputField,
  Button,
  ButtonText,
} from '@/shared/ui';

type FAQItem = {
  id: string;
  question: string;
  answer: string;
  category: string;
};

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do I create a new need?',
    answer:
      'To create a new need, tap the "+" button on the main screen, fill in the details about what you need help with, set your location, and publish it. Other users in your area will be able to see and respond to your need.',
    category: 'Getting Started',
  },
  {
    id: '2',
    question: 'How does payment work?',
    answer:
      'Payments are processed securely through our platform. You can add multiple payment methods in Settings > Payment Methods. Payment is released to the helper only after you confirm the service has been completed satisfactorily.',
    category: 'Payments',
  },
  {
    id: '3',
    question: 'How do I change my notification settings?',
    answer:
      'Go to Settings > Notifications to customize which notifications you receive. You can control push notifications, email notifications, and set quiet hours.',
    category: 'Settings',
  },
  {
    id: '4',
    question: 'What if I have a problem with a helper?',
    answer:
      'If you experience any issues, you can report the user through their profile or contact our support team. We take all reports seriously and will investigate promptly.',
    category: 'Safety',
  },
  {
    id: '5',
    question: 'How do I delete my account?',
    answer:
      'You can delete your account by going to Settings > Privacy Settings > Delete Account. Please note that this action is permanent and cannot be undone.',
    category: 'Account',
  },
];

export default function HelpPage() {
  const router = useRouter();
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [feedbackText, setFeedbackText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    'all',
    'Getting Started',
    'Payments',
    'Settings',
    'Safety',
    'Account',
  ];

  const filteredFAQs =
    selectedCategory === 'all'
      ? faqData
      : faqData.filter((faq) => faq.category === selectedCategory);

  const handleContactSupport = (method: 'email' | 'chat') => {
    if (method === 'email') {
      Linking.openURL('mailto:<EMAIL>?subject=Support Request');
    } else {
      // Implement chat functionality
      Alert.alert('Live Chat', 'Live chat will be available soon!');
    }
  };

  const handleSubmitFeedback = () => {
    if (feedbackText.trim()) {
      Alert.alert(
        'Feedback Sent',
        'Thank you for your feedback! We appreciate your input.',
        [{ text: 'OK', onPress: () => setFeedbackText('') }]
      );
    }
  };

  const openExternalLink = (url: string) => {
    Linking.openURL(url);
  };

  const FAQItem = ({ faq }: { faq: FAQItem }) => {
    const isExpanded = expandedFAQ === faq.id;

    return (
      <Box className="bg-white rounded-xl shadow-sm border border-gray-100 mb-3 overflow-hidden">
        <TouchableOpacity
          onPress={() => setExpandedFAQ(isExpanded ? null : faq.id)}
          className="p-4"
        >
          <HStack space="md" className="items-center">
            <VStack className="flex-1" space="xs">
              <Text className="text-gray-900 font-semibold text-base">
                {faq.question}
              </Text>
              <Text className="text-blue-600 text-sm">{faq.category}</Text>
            </VStack>
            {isExpanded ? (
              <ChevronUp size={20} color="#6B7280" />
            ) : (
              <ChevronDown size={20} color="#6B7280" />
            )}
          </HStack>
        </TouchableOpacity>

        {isExpanded && (
          <Box className="px-4 pb-4 border-t border-gray-100">
            <Text className="text-gray-600 leading-relaxed">{faq.answer}</Text>
          </Box>
        )}
      </Box>
    );
  };

  const ContactOption = ({
    icon: IconComponent,
    title,
    subtitle,
    onPress,
    iconColor,
    iconBgColor,
  }: {
    icon: any;
    title: string;
    subtitle: string;
    onPress: () => void;
    iconColor: string;
    iconBgColor: string;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3"
    >
      <HStack space="md" className="items-center">
        <Box
          className="w-10 h-10 rounded-xl items-center justify-center"
          style={{ backgroundColor: iconBgColor }}
        >
          <IconComponent size={20} color={iconColor} />
        </Box>

        <VStack className="flex-1" space="xs">
          <Text className="text-gray-900 font-semibold text-base">{title}</Text>
          <Text className="text-gray-500 text-sm">{subtitle}</Text>
        </VStack>

        <ChevronRight size={20} color="#9CA3AF" />
      </HStack>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Help & Support
            </Heading>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Quick Actions */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Get Help Now
            </Text>

            <ContactOption
              icon={MessageCircle}
              title="Live Chat"
              subtitle="Chat with our support team"
              onPress={() => handleContactSupport('chat')}
              iconColor="#10B981"
              iconBgColor="#F0FDF4"
            />

            <ContactOption
              icon={Mail}
              title="Email Support"
              subtitle="Send us an email"
              onPress={() => handleContactSupport('email')}
              iconColor="#3B82F6"
              iconBgColor="#EFF6FF"
            />

            <ContactOption
              icon={Bug}
              title="Report a Bug"
              subtitle="Found an issue? Let us know"
              onPress={() => openExternalLink('https://needit.com/report-bug')}
              iconColor="#F59E0B"
              iconBgColor="#FFFBEB"
            />
          </VStack>

          {/* FAQ Section */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Frequently Asked Questions
            </Text>

            {/* Category Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              className="mb-4"
            >
              <HStack space="sm" className="px-2">
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    onPress={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-xl ${
                      selectedCategory === category
                        ? 'bg-blue-600'
                        : 'bg-white border border-gray-200'
                    }`}
                  >
                    <Text
                      className={`font-medium ${
                        selectedCategory === category
                          ? 'text-white'
                          : 'text-gray-700'
                      }`}
                    >
                      {category === 'all' ? 'All' : category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </HStack>
            </ScrollView>

            {filteredFAQs.map((faq) => (
              <FAQItem key={faq.id} faq={faq} />
            ))}
          </VStack>

          {/* Resources */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Resources
            </Text>

            <ContactOption
              icon={FileText}
              title="User Guide"
              subtitle="Complete guide to using NeedIt"
              onPress={() => openExternalLink('https://needit.com/guide')}
              iconColor="#8B5CF6"
              iconBgColor="#F5F3FF"
            />

            <ContactOption
              icon={FileText}
              title="Terms of Service"
              subtitle="Read our terms and conditions"
              onPress={() => openExternalLink('https://needit.com/terms')}
              iconColor="#6B7280"
              iconBgColor="#F9FAFB"
            />

            <ContactOption
              icon={FileText}
              title="Privacy Policy"
              subtitle="How we protect your data"
              onPress={() => openExternalLink('https://needit.com/privacy')}
              iconColor="#6B7280"
              iconBgColor="#F9FAFB"
            />
          </VStack>

          {/* Feedback Section */}
          <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
              Send Feedback
            </Text>

            <VStack space="md">
              <Input className="border-gray-200">
                <InputField
                  value={feedbackText}
                  onChangeText={setFeedbackText}
                  placeholder="Tell us what you think or suggest improvements..."
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </Input>

              <Button
                onPress={handleSubmitFeedback}
                className="bg-blue-600"
                disabled={!feedbackText.trim()}
              >
                <HStack space="sm" className="items-center">
                  <Send size={16} color="white" />
                  <ButtonText className="text-white font-semibold">
                    Send Feedback
                  </ButtonText>
                </HStack>
              </Button>
            </VStack>
          </Box>

          {/* App Info */}
          <Box className="bg-gray-100 rounded-xl p-4">
            <VStack space="sm" className="items-center">
              <Text className="text-gray-600 font-semibold">NeedIt App</Text>
              <Text className="text-gray-500 text-sm">Version 1.0.0</Text>
              <Text className="text-gray-500 text-sm text-center">
                Made with ❤️ for helping communities connect
              </Text>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
