-- Create user_settings table
CREATE TABLE IF NOT EXISTS "user_settings" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" uuid NOT NULL UNIQUE,
    "language" text NOT NULL DEFAULT 'en',
    "dark_mode" boolean NOT NULL DEFAULT false,
    "notifications_enabled" boolean NOT NULL DEFAULT true,
    "email_notifications" boolean NOT NULL DEFAULT true,
    "push_notifications" boolean NOT NULL DEFAULT true,
    "marketing_emails" boolean NOT NULL DEFAULT false,
    "profile_visibility" text NOT NULL DEFAULT 'public',
    "location_sharing" boolean NOT NULL DEFAULT true,
    "created_at" timestamp NOT NULL DEFAULT now(),
    "updated_at" timestamp DEFAULT now(),
    CONSTRAINT "user_settings_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS "user_settings_user_id_idx" ON "user_settings" ("user_id");

-- Add constraint for profile_visibility values
ALTER TABLE "user_settings" 
ADD CONSTRAINT "user_settings_profile_visibility_check" 
CHECK ("profile_visibility" IN ('public', 'friends', 'private')); 