{"id": "6cc59c1d-7ff6-42e0-8426-1cb41f93719b", "prevId": "106c50d3-787e-4bd7-8276-74cb1bf5c233", "version": "7", "dialect": "postgresql", "tables": {"public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credentials_accounts": {"name": "credentials_accounts", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"credentials_accounts_user_id_users_id_fk": {"name": "credentials_accounts_user_id_users_id_fk", "tableFrom": "credentials_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medias": {"name": "medias", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ref": {"name": "ref", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"medias_ref_unique": {"name": "medias_ref_unique", "nullsNotDistinct": false, "columns": ["ref"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.meeting_proposals": {"name": "meeting_proposals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "offer_id": {"name": "offer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "proposer_id": {"name": "proposer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "receiver_id": {"name": "receiver_id", "type": "uuid", "primaryKey": false, "notNull": true}, "proposed_meeting_time": {"name": "proposed_meeting_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "meeting_proposal_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "qr_code_data": {"name": "qr_code_data", "type": "text", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "declined_at": {"name": "declined_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"meeting_proposals_offer_id_offers_id_fk": {"name": "meeting_proposals_offer_id_offers_id_fk", "tableFrom": "meeting_proposals", "tableTo": "offers", "columnsFrom": ["offer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "meeting_proposals_proposer_id_users_id_fk": {"name": "meeting_proposals_proposer_id_users_id_fk", "tableFrom": "meeting_proposals", "tableTo": "users", "columnsFrom": ["proposer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "meeting_proposals_receiver_id_users_id_fk": {"name": "meeting_proposals_receiver_id_users_id_fk", "tableFrom": "meeting_proposals", "tableTo": "users", "columnsFrom": ["receiver_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "offer_id": {"name": "offer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "message_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'sent'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"messages_offer_id_offers_id_fk": {"name": "messages_offer_id_offers_id_fk", "tableFrom": "messages", "tableTo": "offers", "columnsFrom": ["offer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "messages_sender_id_users_id_fk": {"name": "messages_sender_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.needs": {"name": "needs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"needs_user_id_users_id_fk": {"name": "needs_user_id_users_id_fk", "tableFrom": "needs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "needs_category_id_categories_id_fk": {"name": "needs_category_id_categories_id_fk", "tableFrom": "needs", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.offers": {"name": "offers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "need_id": {"name": "need_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "offer_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"offers_need_id_needs_id_fk": {"name": "offers_need_id_needs_id_fk", "tableFrom": "offers", "tableTo": "needs", "columnsFrom": ["need_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "offers_user_id_users_id_fk": {"name": "offers_user_id_users_id_fk", "tableFrom": "offers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "token_hash": {"name": "token_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_accounts": {"name": "social_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"social_accounts_user_id_users_id_fk": {"name": "social_accounts_user_id_users_id_fk", "tableFrom": "social_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan": {"name": "plan", "type": "subscription_plan", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscriptions_user_id_users_id_fk": {"name": "subscriptions_user_id_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscriptions_user_id_unique": {"name": "subscriptions_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "default": "'en'"}, "dark_mode": {"name": "dark_mode", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "notifications_enabled": {"name": "notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "email_notifications": {"name": "email_notifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "push_notifications": {"name": "push_notifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "marketing_emails": {"name": "marketing_emails", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "profile_visibility": {"name": "profile_visibility", "type": "text", "primaryKey": false, "notNull": true, "default": "'public'"}, "location_sharing": {"name": "location_sharing", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "image_ref": {"name": "image_ref", "type": "text", "primaryKey": false, "notNull": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_number_unique": {"name": "users_phone_number_unique", "nullsNotDistinct": false, "columns": ["phone_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.meeting_proposal_status": {"name": "meeting_proposal_status", "schema": "public", "values": ["pending", "accepted", "declined", "expired"]}, "public.message_status": {"name": "message_status", "schema": "public", "values": ["sent", "failed", "read"]}, "public.offer_status": {"name": "offer_status", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.subscription_plan": {"name": "subscription_plan", "schema": "public", "values": ["monthly", "yearly"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}