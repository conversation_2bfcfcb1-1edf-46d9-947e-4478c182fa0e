import { Stack } from 'expo-router';

export default function MainLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: '#F9FAFB' }, // NeedIt light background
        animation: 'slide_from_right', // Smooth modern transitions
      }}
    >
      {/* Tab Navigation */}
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
          gestureEnabled: false, // Disable swipe back on tabs
        }}
      />

      {/* Individual Need Detail */}
      <Stack.Screen
        name="needs/[id]"
        options={{
          headerShown: false,
          presentation: 'card',
        }}
      />

      {/* Subscription/Premium Modal */}
      <Stack.Screen
        name="subscribe"
        options={{
          presentation: 'modal',
          headerShown: false,
          gestureEnabled: true,
        }}
      />

      {/* User Profile View */}
      <Stack.Screen
        name="profile/[id]"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  );
}
