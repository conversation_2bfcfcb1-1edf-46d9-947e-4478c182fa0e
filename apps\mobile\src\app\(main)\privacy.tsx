import { useRouter } from 'expo-router';
import {
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { useState, useEffect } from 'react';

import {
  ArrowLeft,
  Shield,
  Eye,
  EyeOff,
  MapPin,
  Users,
  Lock,
  Trash2,
  Download,
  FileText,
} from 'lucide-react-native';

import {
  useSettings,
  useUpdatePrivacySettings,
} from '@/shared/model/hooks/use-settings';

import { Box, VStack, HStack, Heading, SafeAreaView } from '@/shared/ui';

export default function PrivacyPage() {
  const router = useRouter();
  const { settings, isLoading } = useSettings();
  const updatePrivacySettings = useUpdatePrivacySettings();

  // Local state for privacy settings
  const [profileVisibility, setProfileVisibility] = useState<
    'public' | 'friends' | 'private'
  >('public');
  const [locationSharing, setLocationSharing] = useState(true);
  const [showOnlineStatus, setShowOnlineStatus] = useState(true);
  const [allowMessagesFromStrangers, setAllowMessagesFromStrangers] =
    useState(true);
  const [shareActivityStatus, setShareActivityStatus] = useState(false);

  // Sync with backend settings
  useEffect(() => {
    if (settings) {
      setProfileVisibility(
        settings.profileVisibility as 'public' | 'friends' | 'private'
      );
      setLocationSharing(settings.locationSharing);
    }
  }, [settings]);

  const handleProfileVisibilityChange = (
    visibility: 'public' | 'friends' | 'private'
  ) => {
    setProfileVisibility(visibility);
    updatePrivacySettings.mutate({
      profileVisibility: visibility,
      locationSharing,
    });
  };

  const handleLocationSharingChange = (value: boolean) => {
    setLocationSharing(value);
    updatePrivacySettings.mutate({
      profileVisibility,
      locationSharing: value,
    });
  };

  const handleDataDownload = () => {
    Alert.alert(
      'Download Your Data',
      'We will prepare a copy of your data and send it to your email address within 48 hours.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Download',
          onPress: () => console.log('Data download requested'),
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Are you absolutely sure?',
              'Type "DELETE" to confirm account deletion.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Confirm Delete',
                  style: 'destructive',
                  onPress: () => console.log('Account deletion confirmed'),
                },
              ]
            );
          },
        },
      ]
    );
  };

  const PrivacyToggle = ({
    icon: IconComponent,
    title,
    subtitle,
    value,
    onValueChange,
    iconColor = '#6B7280',
    iconBgColor = '#F3F4F6',
  }: {
    icon: any;
    title: string;
    subtitle: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    iconColor?: string;
    iconBgColor?: string;
  }) => (
    <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
      <HStack space="md" className="items-center">
        <Box
          className="w-10 h-10 rounded-xl items-center justify-center"
          style={{ backgroundColor: iconBgColor }}
        >
          <IconComponent size={20} color={iconColor} />
        </Box>

        <VStack className="flex-1" space="xs">
          <Text className="text-gray-900 font-semibold text-base">{title}</Text>
          <Text className="text-gray-500 text-sm">{subtitle}</Text>
        </VStack>

        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#F3F4F6', true: '#3B82F6' }}
          thumbColor={value ? '#FFFFFF' : '#9CA3AF'}
        />
      </HStack>
    </Box>
  );

  const ProfileVisibilityOption = ({
    icon: IconComponent,
    title,
    subtitle,
    value,
    selected,
    onSelect,
    iconColor,
    iconBgColor,
  }: {
    icon: any;
    title: string;
    subtitle: string;
    value: 'public' | 'friends' | 'private';
    selected: boolean;
    onSelect: () => void;
    iconColor: string;
    iconBgColor: string;
  }) => (
    <TouchableOpacity
      onPress={onSelect}
      className={`bg-white rounded-xl shadow-sm border p-4 mb-3 ${
        selected ? 'border-blue-500 bg-blue-50' : 'border-gray-100'
      }`}
    >
      <HStack space="md" className="items-center">
        <Box
          className="w-10 h-10 rounded-xl items-center justify-center"
          style={{ backgroundColor: iconBgColor }}
        >
          <IconComponent size={20} color={iconColor} />
        </Box>

        <VStack className="flex-1" space="xs">
          <Text
            className={`font-semibold text-base ${
              selected ? 'text-blue-900' : 'text-gray-900'
            }`}
          >
            {title}
          </Text>
          <Text
            className={`text-sm ${
              selected ? 'text-blue-700' : 'text-gray-500'
            }`}
          >
            {subtitle}
          </Text>
        </VStack>

        {selected && (
          <Box className="w-6 h-6 bg-blue-600 rounded-full items-center justify-center">
            <Shield size={12} color="white" />
          </Box>
        )}
      </HStack>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <Box className="bg-white pb-4 border-b border-gray-100">
        <Box className="px-6 pt-4">
          <HStack space="md" className="items-center">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center"
            >
              <ArrowLeft size={20} color="#6B7280" />
            </TouchableOpacity>
            <Heading className="text-xl font-bold text-gray-900 flex-1">
              Privacy Settings
            </Heading>
          </HStack>
        </Box>
      </Box>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingBottom: 32 }}
      >
        <VStack className="px-6 pt-6" space="lg">
          {/* Profile Visibility */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Profile Visibility
            </Text>

            <ProfileVisibilityOption
              icon={Eye}
              title="Public"
              subtitle="Anyone can see your profile and contact you"
              value="public"
              selected={profileVisibility === 'public'}
              onSelect={() => handleProfileVisibilityChange('public')}
              iconColor="#10B981"
              iconBgColor="#F0FDF4"
            />

            <ProfileVisibilityOption
              icon={Users}
              title="Friends Only"
              subtitle="Only people you've connected with can see your profile"
              value="friends"
              selected={profileVisibility === 'friends'}
              onSelect={() => handleProfileVisibilityChange('friends')}
              iconColor="#3B82F6"
              iconBgColor="#EFF6FF"
            />

            <ProfileVisibilityOption
              icon={EyeOff}
              title="Private"
              subtitle="Your profile is hidden from searches and suggestions"
              value="private"
              selected={profileVisibility === 'private'}
              onSelect={() => handleProfileVisibilityChange('private')}
              iconColor="#EF4444"
              iconBgColor="#FEF2F2"
            />
          </VStack>

          {/* Location & Activity */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Location & Activity
            </Text>

            <PrivacyToggle
              icon={MapPin}
              title="Location Sharing"
              subtitle="Allow others to see your approximate location"
              value={locationSharing}
              onValueChange={handleLocationSharingChange}
              iconColor="#F59E0B"
              iconBgColor="#FFFBEB"
            />

            <PrivacyToggle
              icon={Eye}
              title="Online Status"
              subtitle="Show when you're active or last seen"
              value={showOnlineStatus}
              onValueChange={setShowOnlineStatus}
              iconColor="#10B981"
              iconBgColor="#F0FDF4"
            />

            <PrivacyToggle
              icon={Users}
              title="Activity Status"
              subtitle="Share what you're currently doing"
              value={shareActivityStatus}
              onValueChange={setShareActivityStatus}
              iconColor="#8B5CF6"
              iconBgColor="#F5F3FF"
            />
          </VStack>

          {/* Communication */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Communication
            </Text>

            <PrivacyToggle
              icon={Lock}
              title="Messages from Anyone"
              subtitle="Allow messages from people you haven't connected with"
              value={allowMessagesFromStrangers}
              onValueChange={setAllowMessagesFromStrangers}
              iconColor="#06B6D4"
              iconBgColor="#F0F9FF"
            />
          </VStack>

          {/* Data & Account */}
          <VStack space="sm">
            <Text className="text-sm font-bold text-gray-700 uppercase tracking-wider px-2">
              Data & Account
            </Text>

            <TouchableOpacity
              onPress={handleDataDownload}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3"
            >
              <HStack space="md" className="items-center">
                <Box className="w-10 h-10 bg-blue-100 rounded-xl items-center justify-center">
                  <Download size={20} color="#3B82F6" />
                </Box>
                <VStack className="flex-1" space="xs">
                  <Text className="text-gray-900 font-semibold text-base">
                    Download Your Data
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    Get a copy of all your data
                  </Text>
                </VStack>
              </HStack>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => console.log('Privacy policy')}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3"
            >
              <HStack space="md" className="items-center">
                <Box className="w-10 h-10 bg-gray-100 rounded-xl items-center justify-center">
                  <FileText size={20} color="#6B7280" />
                </Box>
                <VStack className="flex-1" space="xs">
                  <Text className="text-gray-900 font-semibold text-base">
                    Privacy Policy
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    Read our privacy policy
                  </Text>
                </VStack>
              </HStack>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleDeleteAccount}
              className="bg-white rounded-xl shadow-sm border border-red-200 p-4"
            >
              <HStack space="md" className="items-center">
                <Box className="w-10 h-10 bg-red-100 rounded-xl items-center justify-center">
                  <Trash2 size={20} color="#EF4444" />
                </Box>
                <VStack className="flex-1" space="xs">
                  <Text className="text-red-600 font-semibold text-base">
                    Delete Account
                  </Text>
                  <Text className="text-red-500 text-sm">
                    Permanently delete your account and data
                  </Text>
                </VStack>
              </HStack>
            </TouchableOpacity>
          </VStack>

          {/* Privacy Info */}
          <Box className="bg-blue-50 rounded-xl p-4">
            <VStack space="sm">
              <Text className="text-blue-900 font-semibold">
                🔒 Your Privacy Matters
              </Text>
              <Text className="text-blue-800 text-sm">
                We're committed to protecting your privacy. You have full
                control over who can see your information and how it's used.
              </Text>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
