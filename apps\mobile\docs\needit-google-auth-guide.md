# Guide Spécifique pour Configurer l'Authentification Google dans NeedIt

Ce guide vous explique pas à pas comment configurer l'authentification Google spécifiquement pour l'application NeedIt.

## Étape 1: Configurer votre app.json

Avant de créer les identifiants Google, vous devez d'abord définir les identifiants de votre application dans le fichier `app.json`. Actuellement, votre fichier app.json ne contient pas de package name pour Android ni de bundle identifier pour iOS.

Modifiez le fichier `apps/mobile/app.json` pour ajouter ces informations :

```json
{
  "expo": {
    "name": "NeedIt",
    "jsEngine": "jsc",
    "slug": "needit",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/images/logoNeedit.webp",
    "scheme": "needit",
    "userInterfaceStyle": "automatic",
    "newArchEnabled": true,
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.votreentreprise.needit"
    },
    "android": {
      "adaptiveIcon": {
        "backgroundColor": "#ffffff"
      },
      "package": "com.votreentreprise.needit",
      "edgeToEdgeEnabled": true
    },
    "web": {
      "bundler": "metro",
      "output": "static",
      "favicon": "./assets/images/logoNeedit.webp"
    },
    "plugins": [
      "expo-router",
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/splash-icon.png",
          "imageWidth": 200,
          "resizeMode": "contain",
          "backgroundColor": "#ffffff"
        }
      ],
      "expo-secure-store",
      "expo-font",
      "expo-web-browser"
    ],
    "experiments": {
      "typedRoutes": true
    },
    "assetBundlePatterns": [
      "**/*"
    ]
  }
}
```

J'ai déjà configuré le fichier avec l'identifiant `com.needit.app`. Si vous souhaitez utiliser un identifiant différent, remplacez-le par un identifiant qui suit la convention de nommage inversée du domaine (par exemple, `com.votreentreprise.needit`).

## Étape 2: Obtenir l'empreinte SHA-1 pour Android

Pour l'authentification Google sur Android, vous avez besoin de l'empreinte SHA-1 de votre certificat de signature.

### Pour le développement

Exécutez la commande suivante dans votre terminal :

#### Sur Windows :
```
keytool -list -v -keystore %USERPROFILE%\.android\debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### Sur macOS/Linux :
```
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

Cherchez la ligne qui commence par "SHA1:" dans la sortie. Par exemple :
```
SHA1: AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD
```

### Avec Expo

Si vous utilisez Expo, vous pouvez obtenir l'empreinte SHA-1 avec la commande :

```
expo fetch:android:hashes
```

## Étape 3: Créer un projet dans Google Cloud Console

1. Accédez à [Google Cloud Console](https://console.cloud.google.com/)
2. Créez un nouveau projet nommé "NeedIt"
3. Notez l'ID du projet

## Étape 4: Configurer l'écran de consentement OAuth

1. Dans le menu de navigation, allez à "APIs & Services" > "OAuth consent screen"
2. Sélectionnez "External" (pour une utilisation publique) ou "Internal" (pour une utilisation interne à votre organisation)
3. Remplissez les informations requises :
   - Nom de l'application: "NeedIt"
   - Email de support utilisateur: votre email
   - Logo: Téléchargez le logo de NeedIt (optionnel)
   - Domaines autorisés: laissez vide pour le moment
4. Cliquez sur "Save and Continue"
5. Dans la section "Scopes", ajoutez les scopes "email" et "profile"
6. Cliquez sur "Save and Continue"
7. Ajoutez des utilisateurs de test (votre email) si vous êtes en mode "External"
8. Cliquez sur "Save and Continue"
9. Vérifiez le récapitulatif et cliquez sur "Back to Dashboard"

## Étape 5: Créer les identifiants OAuth

### Pour le Web (Web Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "Web application"
4. Nom: "NeedIt Web"
5. Sous "Authorized JavaScript origins", ajoutez :
   - `http://localhost:3000`
   - `http://*************:3000` (l'URL de votre backend mobile dans .env)
6. Sous "Authorized redirect URIs", ajoutez :
   - `http://localhost:3000/auth/google/callback`
   - `http://*************:3000/auth/google/callback`
   - `https://auth.expo.io/@votre-nom-utilisateur/needit` (remplacez par votre nom d'utilisateur Expo)
7. Cliquez sur "Create"
8. Notez le Client ID généré

### Pour Android (Android Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "Android"
4. Nom: "NeedIt Android"
5. Package name: `com.needit.app` (le même que dans app.json)
6. SHA-1 certificate fingerprint: Collez l'empreinte SHA-1 que vous avez obtenue précédemment
7. Cliquez sur "Create"
8. Notez le Client ID généré

### Pour iOS (iOS Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "iOS"
4. Nom: "NeedIt iOS"
5. Bundle ID: `com.needit.app` (le même que dans app.json)
6. Cliquez sur "Create"
7. Notez le Client ID généré

## Étape 6: Activer l'API Google Sign-In

1. Dans le menu de navigation, allez à "APIs & Services" > "Library"
2. Recherchez "Google Identity Services" ou "Google Sign-In API"
3. Cliquez sur l'API dans les résultats de recherche
4. Cliquez sur "Enable"

## Étape 7: Configurer les variables d'environnement

Mettez à jour le fichier `apps/mobile/.env` avec les identifiants que vous avez obtenus :

```
EXPO_PUBLIC_WEB_BACKEND_API_URL="http://localhost:3000"
EXPO_PUBLIC_MOBILE_BACKEND_API_URL="http://*************:3000"
EXPO_PUBLIC_ENCRYPTED_GOOGLE_MAPS_API_KEY="94da1213e14db2b1d19eac2f19b577e9:c6a9c9fc553de784809f4f47bd349b178302a427871f1d9a965751bfa69ae8a9ea4810e2630cec3bf70b9e7676779e1a"

# Google OAuth credentials
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_EXPO_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
```

Remplacez les valeurs par les Client IDs que vous avez notés précédemment.

## Étape 8: Redémarrer l'application

Après avoir mis à jour les variables d'environnement, redémarrez votre application :

```
cd apps/mobile
npx expo start --clear
```

## Étape 9: Tester l'authentification Google

1. Ouvrez l'application sur votre appareil ou émulateur
2. Accédez à l'écran de connexion
3. Appuyez sur le bouton "Continue with Google"
4. Vous devriez être redirigé vers l'écran de connexion Google
5. Après avoir sélectionné votre compte, vous devriez être redirigé vers l'application et être connecté

## Dépannage

Si vous rencontrez des problèmes :

1. Vérifiez que tous les identifiants sont correctement configurés dans le fichier `.env`
2. Assurez-vous que les URI de redirection sont correctement configurées dans la console Google Cloud
3. Vérifiez les journaux de l'application pour les erreurs spécifiques
4. Pour les applications Expo, assurez-vous que vous utilisez la dernière version de `expo-auth-session`

### Erreurs courantes

- **"Invalid client ID"** : Vérifiez que vous avez correctement copié le client ID dans le fichier .env
- **"Error 400: redirect_uri_mismatch"** : Vérifiez que l'URI de redirection est correctement configurée dans la console Google Cloud
- **"Error 403: org_internal"** : Si vous utilisez un compte Google Workspace, assurez-vous que l'application est configurée pour les utilisateurs externes

## Ressources supplémentaires

- [Documentation Google OAuth 2.0](https://developers.google.com/identity/protocols/oauth2)
- [Documentation Expo Auth Session](https://docs.expo.dev/versions/latest/sdk/auth-session/)
- [Guide d'authentification Google pour React Native](https://docs.expo.dev/guides/authentication/#google)
