import React from 'react';
import { SignInForm } from '@/pages/auth';
import { Box, Heading, Text, VStack } from '@/shared/ui';
import {
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';

const { height: screenHeight } = Dimensions.get('window');

export default function SignInPage() {
  const router = useRouter();
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'center',
            minHeight: screenHeight,
            paddingVertical: 40,
          }}
          className="bg-gray-50"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Box className="flex-1 bg-gray-50 justify-center">
            {/* Header minimaliste */}
            <Box className="px-6 mb-8">
              <VStack className="items-center" space="sm">
                <Heading className="text-3xl font-bold text-gray-900">
                  Welcome back
                </Heading>
                <Text className="text-gray-600 text-center">
                  Sign in to continue
                </Text>
              </VStack>
            </Box>

            {/* Form */}
            <SignInForm />
          </Box>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
