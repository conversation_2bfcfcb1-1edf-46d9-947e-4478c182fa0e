import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';

import status from 'http-status';

import { applyDtoSchema } from '@/shared/lib';
import { requireAuthMiddleware } from '@/shared/middlewares';
import type { AuthVariables } from '@/shared/model';

import {
  activityTimelineResponseDto,
  profilePictureUpdateDto,
  profileResponseDto,
  profileStatsResponseDto,
  profileUpdateDto,
  recentNeedsResponseDto,
  recentOffersResponseDto,
  recentMessagesResponseDto,
} from './profile.dto';
import { profileService } from './profile.service';

export const profileRoutes = new Hono<{
  Variables: AuthVariables;
}>();

// Apply authentication middleware to all profile routes
profileRoutes.use('*', requireAuthMiddleware);

/**
 * Get the current user's profile
 */
profileRoutes.get('/', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const profile = await profileService.getProfile(userId);

  if (!profile) {
    throw new HTTPException(status.NOT_FOUND, {
      res: Response.json({
        error: {
          code: status[`${status.NOT_FOUND}_NAME`],
          message: `Profile not found for user ${userId}`,
        },
      }),
    });
  }

  const profileDto = applyDtoSchema(profile, profileResponseDto);

  return c.json({ profile: profileDto });
});

/**
 * Update the current user's profile
 */
profileRoutes.patch(
  '/',
  zValidator('json', profileUpdateDto),
  async (c) => {
    const session = c.get('session')!;
    const userId = session.user.id;
    const data = c.req.valid('json');

    const updatedProfile = await profileService.updateProfile(userId, data);

    if (!updatedProfile) {
      throw new HTTPException(status.NOT_FOUND, {
        res: Response.json({
          error: {
            code: status[`${status.NOT_FOUND}_NAME`],
            message: `Profile not found for user ${userId}`,
          },
        }),
      });
    }

    const profileDto = applyDtoSchema(updatedProfile, profileResponseDto);

    return c.json({ profile: profileDto });
  }
);

/**
 * Update the current user's profile picture
 */
profileRoutes.patch(
  '/picture',
  zValidator('json', profilePictureUpdateDto),
  async (c) => {
    const session = c.get('session')!;
    const userId = session.user.id;
    const { imageRef } = c.req.valid('json');

    const updatedProfile = await profileService.updateProfilePicture(
      userId,
      imageRef
    );

    if (!updatedProfile) {
      throw new HTTPException(status.NOT_FOUND, {
        res: Response.json({
          error: {
            code: status[`${status.NOT_FOUND}_NAME`],
            message: `Profile not found for user ${userId}`,
          },
        }),
      });
    }

    const profileDto = applyDtoSchema(updatedProfile, profileResponseDto);

    return c.json({ profile: profileDto });
  }
);

/**
 * Get the current user's profile statistics
 */
profileRoutes.get('/stats', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const stats = await profileService.getProfileStats(userId);

  const statsDto = applyDtoSchema(stats, profileStatsResponseDto);

  return c.json({ stats: statsDto });
});

/**
 * Get the current user's recent needs
 */
profileRoutes.get('/needs', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const needs = await profileService.getRecentNeeds(userId);

  // No need to apply DTO schema for arrays, just return the data
  return c.json({ needs });
});

/**
 * Get the current user's recent offers
 */
profileRoutes.get('/offers', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const offers = await profileService.getRecentOffers(userId);

  // No need to apply DTO schema for arrays, just return the data
  return c.json({ offers });
});

/**
 * Get the current user's recent messages
 */
profileRoutes.get('/messages', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const messages = await profileService.getRecentMessages(userId);

  // No need to apply DTO schema for arrays, just return the data
  return c.json({ messages });
});

/**
 * Get the current user's activity timeline
 */
profileRoutes.get('/timeline', async (c) => {
  const session = c.get('session')!;
  const userId = session.user.id;

  const timeline = await profileService.getActivityTimeline(userId);

  // No need to apply DTO schema for arrays, just return the data
  return c.json({ timeline });
});
