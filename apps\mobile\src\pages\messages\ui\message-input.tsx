import React, { useState, useCallback } from 'react';
import {
  TouchableOpacity,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {
  VStack,
  HStack,
  Input,
  InputField,
  Text,
  Icon,
  Box,
} from '@/shared/ui';
import {
  Send,
  Calendar,
  Camera,
  Image as ImageIcon,
  Plus,
} from 'lucide-react-native';

interface MessageInputProps {
  message: string;
  setMessage: (message: string) => void;
  onSend: () => void;
  onMakeOffer?: () => void;
  showMakeOfferButton?: boolean;
}

export const MessageInput = React.memo(
  ({
    message,
    setMessage,
    onSend,
    onMakeOffer,
    showMakeOfferButton = false,
  }: MessageInputProps) => {
    const [showActionButtons, setShowActionButtons] = useState(false);
    const [isInputFocused, setIsInputFocused] = useState(false);

    const handleToggleActions = useCallback(() => {
      setShowActionButtons((prev) => !prev);
    }, []);

    const handleMakeOffer = useCallback(() => {
      if (onMakeOffer) {
        onMakeOffer();
        setShowActionButtons(false);
      }
    }, [onMakeOffer]);

    const handleCameraPick = useCallback(async () => {
      try {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission required',
            'Camera permission is needed to take photos'
          );
          return;
        }

        const result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });

        if (!result.canceled && result.assets?.[0]) {
          // Handle image
          console.log('Camera image:', result.assets[0]);
        }
      } catch (error) {
        console.error('Error taking photo:', error);
        Alert.alert('Error', 'Failed to take photo');
      }
    }, []);

    const handleImagePick = useCallback(async () => {
      try {
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission required',
            'Photo library permission is needed to select images'
          );
          return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });

        if (!result.canceled && result.assets?.[0]) {
          // Handle image
          console.log('Gallery image:', result.assets[0]);
        }
      } catch (error) {
        console.error('Error picking image:', error);
        Alert.alert('Error', 'Failed to pick image');
      }
    }, []);

    const handleInputFocus = useCallback(() => {
      setIsInputFocused(true);
    }, []);

    const handleInputBlur = useCallback(() => {
      setIsInputFocused(false);
    }, []);

    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
        keyboardVerticalOffset={
          isInputFocused ? (Platform.OS === 'ios' ? 220 : 240) : 0
        }
        style={{
          width: '100%',
        }}
      >
        <Box className="bg-white border-t border-gray-100 shadow-sm">
          <VStack space="md" className="p-6">
            {/* Action Buttons Row - when expanded */}
            {showActionButtons && (
              <Box className="bg-gray-50 rounded-xl border border-gray-200 p-4">
                <HStack space="sm">
                  {/* Meeting Proposal Button */}
                  {showMakeOfferButton && onMakeOffer && (
                    <TouchableOpacity
                      onPress={handleMakeOffer}
                      className="bg-blue-600 rounded-xl h-14 flex-1 items-center justify-center shadow-sm"
                    >
                      <HStack className="items-center" space="sm">
                        <Icon as={Calendar} size="md" color="#FFFFFF" />
                        <Text className="text-white font-body" size="md" bold>
                          Propose
                        </Text>
                      </HStack>
                    </TouchableOpacity>
                  )}

                  {/* Camera Button */}
                  <TouchableOpacity
                    onPress={handleCameraPick}
                    className="bg-white rounded-xl h-14 flex-1 items-center justify-center border border-gray-200 shadow-sm"
                  >
                    <HStack className="items-center" space="sm">
                      <Icon as={Camera} size="md" color="#6B7280" />
                      <Text className="text-gray-600 font-body" size="md" bold>
                        Camera
                      </Text>
                    </HStack>
                  </TouchableOpacity>

                  {/* Gallery Button */}
                  <TouchableOpacity
                    onPress={handleImagePick}
                    className="bg-white rounded-xl h-14 flex-1 items-center justify-center border border-gray-200 shadow-sm"
                  >
                    <HStack className="items-center" space="sm">
                      <Icon as={ImageIcon} size="md" color="#6B7280" />
                      <Text className="text-gray-600 font-body" size="md" bold>
                        Gallery
                      </Text>
                    </HStack>
                  </TouchableOpacity>
                </HStack>
              </Box>
            )}

            {/* Main Input Row */}
            <HStack space="sm" className="items-end">
              {/* Action Toggle Button */}
              <TouchableOpacity
                onPress={handleToggleActions}
                className={`w-12 h-12 rounded-xl items-center justify-center border shadow-sm ${
                  showActionButtons
                    ? 'bg-blue-600 border-blue-600'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <Icon
                  as={Plus}
                  size="md"
                  color={showActionButtons ? '#FFFFFF' : '#6B7280'}
                  style={{
                    transform: [
                      { rotate: showActionButtons ? '45deg' : '0deg' },
                    ],
                  }}
                />
              </TouchableOpacity>

              {/* Text Input */}
              <Box className="flex-1">
                <Input className="bg-gray-50 border-gray-200 rounded-xl shadow-sm">
                  <InputField
                    placeholder="Type your message..."
                    value={message}
                    onChangeText={setMessage}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    multiline
                    maxLength={1000}
                    className="px-4 font-body text-gray-900"
                    style={{
                      minHeight: 48,
                      maxHeight: 120,
                      paddingTop: 14,
                      paddingBottom: 14,
                      fontSize: 16,
                      lineHeight: 20,
                    }}
                  />
                </Input>
              </Box>

              {/* Send Button */}
              <TouchableOpacity
                onPress={onSend}
                disabled={!message.trim()}
                className={`w-12 h-12 rounded-xl items-center justify-center shadow-sm ${
                  message.trim()
                    ? 'bg-blue-600 shadow-lg shadow-blue-600/25'
                    : 'bg-gray-200'
                }`}
              >
                <Icon
                  as={Send}
                  size="md"
                  color={message.trim() ? '#FFFFFF' : '#9CA3AF'}
                />
              </TouchableOpacity>
            </HStack>
          </VStack>
        </Box>
      </KeyboardAvoidingView>
    );
  }
);

MessageInput.displayName = 'MessageInput';
