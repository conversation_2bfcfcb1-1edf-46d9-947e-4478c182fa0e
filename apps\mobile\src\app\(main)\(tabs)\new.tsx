import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import React from 'react';
import {
  ShoppingCart,
  Heart,
  Car,
  Hammer,
  ShoppingBag,
  Home,
  Leaf,
  Laptop,
  Zap,
  Apple,
  Dog,
  Wrench,
  Baby,
  GraduationCap,
  Briefcase,
  Shirt,
  Music,
  Camera,
  Gamepad2,
  Paintbrush,
  Users,
  Globe,
} from 'lucide-react-native';

import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  AlertCircleIcon,
  Button,
  ButtonText,
  Center,
  ChevronDownIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  Input,
  InputField,
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectTrigger,
  VStack,
  Spinner,
  Text,
  Box,
  Heading,
  HStack,
} from '@/shared/ui';
import { createNeedSchema, getCategories } from '@/pages/needs';
import { useNeedStore } from '@/shared/model';
import { useQuery } from '@tanstack/react-query';

export default function NeedForm() {
  const router = useRouter();
  const { addNeed } = useNeedStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cachedLocation, setCachedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Pre-fetch location when component mounts
  useEffect(() => {
    const getLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          try {
            // Try to get location with high accuracy first
            console.log('Getting location with high accuracy...');
            const location = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.High,
            });

            console.log('Got high accuracy location:', location.coords);
            setCachedLocation({
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            });
          } catch (highAccuracyError) {
            console.log('High accuracy location failed:', highAccuracyError);

            try {
              // If high accuracy fails, try with lower accuracy
              console.log('Getting location with balanced accuracy...');
              const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
              });

              console.log('Got balanced accuracy location:', location.coords);
              setCachedLocation({
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              });
            } catch (lowAccuracyError) {
              console.log('All location attempts failed, using default');
              setCachedLocation({
                latitude: 49.03985836191832,
                longitude: 2.0781613024585637,
              });
            }
          }
        } else {
          console.log('Location permission denied, using default');
          setCachedLocation({
            latitude: 49.03985836191832,
            longitude: 2.0781613024585637,
          });
        }
      } catch (err) {
        console.log('Error pre-fetching location:', err);
        setCachedLocation({
          latitude: 49.03985836191832,
          longitude: 2.0781613024585637,
        });
      }
    };

    getLocation();
  }, []);

  const { data } = useQuery({
    queryKey: ['categories'],
    queryFn: () => getCategories(),
  });

  const form = useForm<z.infer<typeof createNeedSchema>>({
    resolver: zodResolver(createNeedSchema),
  });

  const handleCreation = async () => {
    setIsSubmitting(true);
    setError(null);
    const { title, description, category: categoryId } = form.getValues();

    try {
      // Use cached location if available to avoid delay
      let locationCoords;

      if (cachedLocation) {
        locationCoords = cachedLocation;
        console.log('Using cached location:', locationCoords);
      } else {
        // Try to get a fresh location with better handling for real devices
        try {
          console.log('Getting fresh location with high accuracy...');
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
          });

          console.log('Got fresh high accuracy location:', location.coords);
          locationCoords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
        } catch (highAccuracyError) {
          console.log(
            'Fresh high accuracy location failed:',
            highAccuracyError
          );

          try {
            // If high accuracy fails, try with lower accuracy
            console.log('Getting fresh location with balanced accuracy...');
            const location = await Location.getCurrentPositionAsync({
              accuracy: Location.Accuracy.Balanced,
            });

            console.log(
              'Got fresh balanced accuracy location:',
              location.coords
            );
            locationCoords = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            };
          } catch (lowAccuracyError) {
            console.log('All fresh location attempts failed, using default');
            locationCoords = {
              latitude: 49.03985836191832,
              longitude: 2.0781613024585637,
            };
          }
        }
      }

      // Use the need store to add the need
      await addNeed({
        title,
        description,
        categoryId,
        location: {
          x: locationCoords.latitude,
          y: locationCoords.longitude,
        },
      });

      // Navigate back to the map screen
      router.push('/');
    } catch (error) {
      console.error('Error creating need:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to create need'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Form Content */}
      <VStack space="md" className="px-6 pt-20 flex-1">
        {/* Title Card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4">
          <Heading className="text-2xl font-bold text-gray-900 mb-2 text-center">
            Create Need
          </Heading>
          <Text className="text-gray-600 text-base font-medium text-center">
            What can the community help you with?
          </Text>
        </Box>

        {/* Category Selection */}
        <Box>
          <Text className="text-sm font-bold text-gray-700 mb-3 uppercase tracking-wider">
            Category
          </Text>
          <Controller
            control={form.control}
            name="category"
            render={({ field, fieldState }) => {
              const getCategoryIcon = (categoryName: string) => {
                switch (categoryName.toLowerCase()) {
                  case 'food':
                  case 'alimentaire':
                    return <Apple size={18} color="#10B981" />;
                  case 'animals':
                  case 'animaux':
                  case 'pets':
                    return <Dog size={18} color="#8B5CF6" />;
                  case 'transport':
                  case 'transportation':
                    return <Car size={18} color="#3B82F6" />;
                  case 'diy':
                  case 'bricolage':
                  case 'repairs':
                  case 'réparations':
                    return <Hammer size={18} color="#F59E0B" />;
                  case 'shopping':
                  case 'courses':
                  case 'groceries':
                    return <ShoppingBag size={18} color="#EF4444" />;
                  case 'cleaning':
                  case 'ménage':
                  case 'housework':
                    return <Home size={18} color="#06B6D4" />;
                  case 'gardening':
                  case 'jardinage':
                  case 'garden':
                    return <Leaf size={18} color="#84CC16" />;
                  case 'technology':
                  case 'gaming':
                  case 'technologie':
                  case 'tech':
                    return <Laptop size={18} color="#6366F1" />;
                  case 'health':
                  case 'santé':
                  case 'medical':
                    return <Heart size={18} color="#EF4444" />;
                  case 'childcare':
                  case "garde d'enfants":
                  case 'babysitting':
                    return <Baby size={18} color="#F59E0B" />;
                  case 'education':
                  case 'éducation':
                  case 'learning':
                  case 'tutoring':
                    return <GraduationCap size={18} color="#8B5CF6" />;
                  case 'work':
                  case 'travail':
                  case 'professional':
                    return <Briefcase size={18} color="#1F2937" />;
                  case 'clothing':
                  case 'vêtements':
                  case 'fashion':
                    return <Shirt size={18} color="#EC4899" />;
                  case 'music':
                  case 'musique':
                  case 'instruments':
                    return <Music size={18} color="#7C3AED" />;
                  case 'photography':
                  case 'photographie':
                  case 'photo':
                    return <Camera size={18} color="#059669" />;
                  case 'art':
                  case 'arts':
                  case 'creative':
                    return <Paintbrush size={18} color="#DC2626" />;
                  case 'community':
                  case 'communauté':
                  case 'social':
                    return <Users size={18} color="#0891B2" />;
                  case 'events':
                  case 'événements':
                  case 'party':
                    return <Globe size={18} color="#7C2D12" />;
                  default:
                    return <Zap size={18} color="#6B7280" />;
                }
              };

              const selectedCategory = data?.find(
                (cat) => cat.id === field.value
              );

              return (
                <FormControl isInvalid={fieldState.error !== undefined}>
                  <Box className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                    <Select {...field} onValueChange={field.onChange}>
                      <SelectTrigger
                        variant="outline"
                        size="lg"
                        className="border-0 bg-transparent h-16 px-4"
                      >
                        <Box className="flex-row items-center flex-1">
                          {selectedCategory && (
                            <Box className="mr-3">
                              {getCategoryIcon(selectedCategory.name)}
                            </Box>
                          )}
                          <SelectInput
                            placeholder={
                              selectedCategory?.name || 'Select a category'
                            }
                            className="text-gray-900 text-base font-medium flex-1"
                          />
                        </Box>
                        <SelectIcon
                          className="mr-2 text-gray-400"
                          as={ChevronDownIcon}
                        />
                      </SelectTrigger>
                      <SelectPortal>
                        <SelectBackdrop className="bg-black/40" />
                        <SelectContent className="bg-white rounded-t-3xl border-t border-gray-100">
                          <SelectDragIndicatorWrapper className="py-4">
                            <SelectDragIndicator className="bg-gray-300 w-12 h-1 rounded-full" />
                          </SelectDragIndicatorWrapper>
                          <Box className="px-4 pb-2">
                            <Text className="text-lg font-bold text-gray-900 text-center">
                              Choose Category
                            </Text>
                          </Box>
                          {data?.map((category) => {
                            return (
                              <SelectItem
                                key={category.id}
                                label={category.name}
                                value={category.id}
                                className="py-4 px-6 border-b border-gray-50 last:border-b-0"
                              >
                                <Box className="flex-row items-center">
                                  <Box className="mr-3">
                                    {getCategoryIcon(category.name)}
                                  </Box>
                                  <Text className="text-gray-900 text-base font-medium">
                                    {category.name}
                                  </Text>
                                </Box>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </SelectPortal>
                    </Select>
                  </Box>
                  {fieldState.error && (
                    <FormControlError className="mt-2">
                      <FormControlErrorIcon
                        as={AlertCircleIcon}
                        className="text-red-500"
                      />
                      <FormControlErrorText className="text-red-500 font-medium">
                        {fieldState.error?.message}
                      </FormControlErrorText>
                    </FormControlError>
                  )}
                </FormControl>
              );
            }}
          />
        </Box>

        {/* Title */}
        <Box>
          <Text className="text-sm font-bold text-gray-700 mb-3 uppercase tracking-wider">
            Title
          </Text>
          <Controller
            control={form.control}
            name="title"
            render={({ field, fieldState }) => (
              <FormControl isInvalid={fieldState.error !== undefined}>
                <Box className="bg-white rounded-xl border border-gray-200">
                  <Input
                    size="xl"
                    variant="outline"
                    className="border-0 bg-transparent"
                  >
                    <InputField
                      placeholder="What do you need?"
                      {...field}
                      onChangeText={field.onChange}
                      className="text-xl font-bold text-gray-900 placeholder:text-gray-400 px-4 py-4"
                    />
                  </Input>
                </Box>
                {fieldState.error && (
                  <FormControlError className="mt-2">
                    <FormControlErrorIcon
                      as={AlertCircleIcon}
                      className="text-red-500"
                    />
                    <FormControlErrorText className="text-red-500 font-medium">
                      {fieldState.error?.message}
                    </FormControlErrorText>
                  </FormControlError>
                )}
              </FormControl>
            )}
          />
        </Box>

        {/* Description */}
        <Box className="flex-1">
          <Text className="text-sm font-bold text-gray-700 mb-3 uppercase tracking-wider">
            Description
          </Text>
          <Controller
            control={form.control}
            name="description"
            render={({ field, fieldState }) => (
              <FormControl isInvalid={fieldState.error !== undefined}>
                <Box className="bg-white rounded-xl border border-gray-200 min-h-32">
                  <Input
                    variant="outline"
                    size="xl"
                    className="border-0 bg-transparent h-full"
                  >
                    <InputField
                      placeholder="Provide details about your need..."
                      {...field}
                      onChangeText={field.onChange}
                      multiline
                      numberOfLines={6}
                      textAlignVertical="top"
                      className="text-gray-900 text-base placeholder:text-gray-400 px-4 py-4 min-h-28 font-normal"
                    />
                  </Input>
                </Box>
                {fieldState.error && (
                  <FormControlError className="mt-2">
                    <FormControlErrorIcon
                      as={AlertCircleIcon}
                      className="text-red-500"
                    />
                    <FormControlErrorText className="text-red-500 font-medium">
                      {fieldState.error?.message}
                    </FormControlErrorText>
                  </FormControlError>
                )}
              </FormControl>
            )}
          />
        </Box>
      </VStack>

      {/* Error State */}
      {error && (
        <Box className="mx-6 mb-4">
          <Box className="bg-red-50 border border-red-200 rounded-xl p-4">
            <Text className="text-red-600 text-center font-medium">
              {error}
            </Text>
          </Box>
        </Box>
      )}

      {/* Bottom Action Area */}
      <Box className="bg-white border-t border-gray-100 p-6">
        <Button
          size="xl"
          variant="solid"
          className="bg-blue-600 hover:bg-blue-700 rounded-xl h-14 shadow-sm"
          onPress={form.handleSubmit(handleCreation)}
          isDisabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Spinner size="small" color="white" className="mr-3" />
              <ButtonText className="text-white text-base font-bold">
                Publishing...
              </ButtonText>
            </>
          ) : (
            <ButtonText className="text-white text-base font-bold">
              Publish Need
            </ButtonText>
          )}
        </Button>

        <Text className="text-gray-500 text-center mt-3 text-sm font-normal">
          Your need will be visible to nearby users
        </Text>
      </Box>
    </Box>
  );
}
