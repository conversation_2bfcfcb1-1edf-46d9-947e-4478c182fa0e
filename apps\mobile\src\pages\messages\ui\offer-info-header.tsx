import React from 'react';
import { View } from 'react-native';
import { Text, VStack, HStack, Icon, ClockIcon } from '@/shared/ui';
import { MapPin } from 'lucide-react-native';

interface OfferInfoHeaderProps {
  title: string;
  description: string;
  price: string;
  status: string;
  location: string;
}

export function OfferInfoHeader({
  title,
  description,
  price,
  status,
  location,
}: OfferInfoHeaderProps) {
  return (
    <View className="bg-gray-50 mx-3 mt-2 rounded-lg px-3 py-2">
      <VStack space="xs">
        {/* Title and Price */}
        <HStack className="justify-between items-start">
          <VStack space="xs" className="flex-1 mr-2">
            <Text
              className="font-medium text-sm text-gray-900"
              numberOfLines={1}
            >
              {title}
            </Text>
            <Text className="text-xs text-gray-500" numberOfLines={1}>
              {description}
            </Text>
          </VStack>
          <Text className="font-semibold text-sm text-blue-600">{price}€</Text>
        </HStack>

        {/* Location and Status in one line */}
        <HStack className="justify-between items-center">
          <HStack className="items-center" space="xs">
            <Icon as={MapPin} size="xs" className="text-gray-400" />
            <Text className="text-xs text-gray-500" numberOfLines={1}>
              {location}
            </Text>
          </HStack>
          <HStack className="items-center" space="xs">
            <Icon as={ClockIcon} size="xs" className="text-gray-400" />
            <Text className="text-xs text-gray-500">{status}</Text>
          </HStack>
        </HStack>
      </VStack>
    </View>
  );
}
