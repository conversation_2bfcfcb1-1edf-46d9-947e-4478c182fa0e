import { useState, useEffect } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import {
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  OTPForm,
  otpSchema,
  OtpSchema,
  sendOtp,
  useSignUpStore,
  verifyOtp,
} from '@/pages/auth';

import {
  Button,
  ButtonText,
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Spinner,
} from '@/shared/ui';

const { height: screenHeight } = Dimensions.get('window');

export default function SignUpOtpPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [userId, setUserId] = useState<string | null>(null);
  const { credentials, reset, isSocialAccount } = useSignUpStore();

  // Check for userId in route params
  useEffect(() => {
    if (params.userId) {
      setUserId(params.userId as string);
    }
  }, [params]);

  const form = useForm<OtpSchema>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      code: '',
    },
  });

  // Send OTP on component mount
  useEffect(() => {
    sendOtpCode();
  }, []);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Send OTP code
  const sendOtpCode = async () => {
    try {
      if (!userId) {
        throw new Error('User ID not available');
      }

      setIsLoading(true);

      // Send email verification OTP
      await sendOtp({
        type: 'email-verification',
        userId,
      });

      // Reset countdown
      setCountdown(60);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP code
  const handleVerify = async (data: OtpSchema) => {
    try {
      setIsLoading(true);

      if (!userId) {
        throw new Error('User ID not available');
      }

      // Verify OTP
      await verifyOtp({
        userId,
        otp: data.code,
        type: 'email-verification',
      });

      // Reset sign-up store
      reset();

      // Redirect to sign-in or main page
      router.replace('/sign-in');
    } catch (error) {
      console.error(error);
      form.setError('code', {
        message: 'Invalid verification code. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'center',
            minHeight: screenHeight,
            paddingVertical: 40,
          }}
          className="bg-gray-50"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Box className="flex-1 bg-gray-50 justify-center">
            {/* Header */}
            <Box className="px-6 mb-8">
              <VStack className="items-center" space="sm">
                <Heading className="text-3xl font-bold text-gray-900">
                  Verify your email
                </Heading>
                <Text className="text-gray-600 text-center max-w-sm">
                  {isSocialAccount
                    ? `An account with email ${credentials.email} already exists. Please verify to continue.`
                    : `We've sent a verification code to ${credentials.email}`}
                </Text>
              </VStack>
            </Box>

            {/* Form Container */}
            <Box className="px-6">
              <VStack space="lg" className="w-full max-w-sm mx-auto">
                {/* Main Form Card */}
                <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <VStack space="lg">
                    {/* OTP Form */}
                    <OTPForm form={form} />

                    {/* Verify Button */}
                    <Button
                      className="bg-blue-600 rounded-xl h-14 shadow-sm"
                      onPress={form.handleSubmit(handleVerify)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <HStack className="items-center" space="sm">
                          <Spinner size="small" color="white" />
                          <ButtonText className="text-white font-semibold text-base">
                            Verifying...
                          </ButtonText>
                        </HStack>
                      ) : (
                        <ButtonText className="text-white font-semibold text-base">
                          Verify Email
                        </ButtonText>
                      )}
                    </Button>
                  </VStack>
                </Box>

                {/* Resend Code Section */}
                <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <VStack space="md" className="items-center">
                    <Text className="text-gray-600 text-center">
                      Didn't receive the code?
                    </Text>

                    <Button
                      onPress={() => {
                        if (countdown === 0) {
                          sendOtpCode();
                        }
                      }}
                      variant="outline"
                      className="rounded-xl h-12 border-gray-300 bg-white"
                      disabled={countdown > 0 || isLoading}
                    >
                      <ButtonText className="text-gray-700 font-semibold">
                        {countdown > 0
                          ? `Resend in ${countdown}s`
                          : 'Resend Code'}
                      </ButtonText>
                    </Button>
                  </VStack>
                </Box>
              </VStack>
            </Box>
          </Box>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
