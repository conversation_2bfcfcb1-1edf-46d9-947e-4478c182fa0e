import { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useRouter } from 'expo-router';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  checkEmailExists,
  checkUserHasCredentials,
  credentialsSchema,
  CredentialsSchema,
  SocialSignIn,
  useSignUpStore,
} from '@/pages/auth';
import {
  AlertCircleIcon,
  Button,
  ButtonText,
  EyeIcon,
  EyeOffIcon,
  FormControl,
  FormControlError,
  FormControlErrorIcon,
  FormControlErrorText,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  VStack,
  Box,
  Text,
  Spinner,
  HStack,
  Heading,
} from '@/shared/ui';

const { height: screenHeight } = Dimensions.get('window');

export default function SignUpCredentialsPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [routerReady, setRouterReady] = useState(false);
  const isMountedRef = useRef(true);
  const { credentials, setCredentials, setSocialAccount } = useSignUpStore();

  const form = useForm<CredentialsSchema>({
    resolver: zodResolver(credentialsSchema),
    defaultValues: credentials,
  });

  useEffect(() => {
    setRouterReady(true);
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const safeSetLoading = useCallback((loading: boolean) => {
    if (isMountedRef.current) {
      setIsLoading(loading);
    }
  }, []);

  const handleNext = async (data: CredentialsSchema) => {
    if (!isMountedRef.current || !routerReady) return;

    Keyboard.dismiss();
    safeSetLoading(true);

    try {
      // Save the data to store
      setCredentials(data);

      // Check if user exists
      const userExists = await checkEmailExists(data.email);

      if (userExists) {
        // Check if the user has credentials (password set up)
        const hasCredentials = await checkUserHasCredentials(data.email);

        if (hasCredentials) {
          // User exists with password already set
          form.setError('email', {
            message: 'An account with this email already exists',
          });
          return;
        } else {
          // User exists but without password (social login)
          setSocialAccount(true);
          router.push('/sign-up/otp');
          return;
        }
      }

      // User doesn't exist, continue to profile page
      router.push('/sign-up/profile');
    } catch (error) {
      console.error(error);
      form.setError('confirmPassword', {
        message: 'Sign up failed. Please try again.',
      });
    } finally {
      safeSetLoading(false);
    }
  };

  const handleGoogleSignIn = useCallback(async () => {
    if (!isMountedRef.current) return;
    console.log('Google Sign In initiated');
  }, []);

  const handleAppleSignIn = useCallback(async () => {
    if (!isMountedRef.current) return;
    console.log('Apple Sign In initiated');
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'center',
            minHeight: screenHeight,
            paddingVertical: 40,
          }}
          className="bg-gray-50"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Box className="flex-1 bg-gray-50 justify-center">
            {/* Header minimaliste */}
            <Box className="px-6 mb-8">
              <VStack className="items-center" space="sm">
                <Heading className="text-3xl font-bold text-gray-900">
                  Sign up
                </Heading>
                <Text className="text-gray-600 text-center">
                  Create your account to get started
                </Text>
              </VStack>
            </Box>

            {/* Form Container */}
            <Box className="px-6">
              <VStack space="lg" className="w-full max-w-sm mx-auto">
                {/* Main Form Card */}
                <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <VStack space="lg">
                    {/* Email Field */}
                    <Controller
                      control={form.control}
                      name="email"
                      render={({ field, fieldState }) => (
                        <FormControl isInvalid={fieldState.error !== undefined}>
                          <Text className="text-sm font-semibold text-gray-700 mb-3">
                            Email
                          </Text>
                          <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                            <Input className="border-0 bg-transparent h-full">
                              <InputField
                                placeholder="<EMAIL>"
                                value={field.value}
                                onChangeText={field.onChange}
                                keyboardType="email-address"
                                autoCapitalize="none"
                                autoComplete="email"
                                autoCorrect={false}
                                style={{
                                  fontSize: 16,
                                  lineHeight: 20,
                                  height: 56,
                                  paddingHorizontal: 16,
                                  paddingVertical: 0,
                                  textAlignVertical: 'center',
                                  color: '#111827',
                                  fontWeight: '500',
                                }}
                              />
                            </Input>
                          </Box>
                          {fieldState.error && (
                            <FormControlError className="mt-2">
                              <FormControlErrorIcon
                                as={AlertCircleIcon}
                                className="text-red-500"
                              />
                              <FormControlErrorText className="text-red-500 font-medium">
                                {fieldState.error?.message}
                              </FormControlErrorText>
                            </FormControlError>
                          )}
                        </FormControl>
                      )}
                    />

                    {/* Password Field */}
                    <Controller
                      control={form.control}
                      name="password"
                      render={({ field, fieldState }) => (
                        <FormControl isInvalid={fieldState.error !== undefined}>
                          <Text className="text-sm font-semibold text-gray-700 mb-3">
                            Password
                          </Text>
                          <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                            <Input className="border-0 bg-transparent h-full">
                              <InputField
                                placeholder="Enter your password"
                                value={field.value}
                                onChangeText={field.onChange}
                                secureTextEntry={!showPassword}
                                autoComplete="new-password"
                                autoCorrect={false}
                                style={{
                                  fontSize: 16,
                                  lineHeight: 20,
                                  height: 56,
                                  paddingHorizontal: 16,
                                  paddingRight: 48,
                                  paddingVertical: 0,
                                  textAlignVertical: 'center',
                                  color: '#111827',
                                  fontWeight: '500',
                                }}
                              />
                              <InputSlot
                                className="absolute right-0 h-full w-14 justify-center items-center"
                                onPress={() => setShowPassword(!showPassword)}
                              >
                                <InputIcon
                                  as={showPassword ? EyeOffIcon : EyeIcon}
                                  className="text-gray-400"
                                  size="md"
                                />
                              </InputSlot>
                            </Input>
                          </Box>
                          {fieldState.error && (
                            <FormControlError className="mt-2">
                              <FormControlErrorIcon
                                as={AlertCircleIcon}
                                className="text-red-500"
                              />
                              <FormControlErrorText className="text-red-500 font-medium">
                                {fieldState.error?.message}
                              </FormControlErrorText>
                            </FormControlError>
                          )}
                        </FormControl>
                      )}
                    />

                    {/* Confirm Password Field */}
                    <Controller
                      control={form.control}
                      name="confirmPassword"
                      render={({ field, fieldState }) => (
                        <FormControl isInvalid={fieldState.error !== undefined}>
                          <Text className="text-sm font-semibold text-gray-700 mb-3">
                            Confirm Password
                          </Text>
                          <Box className="bg-gray-50 rounded-xl border border-gray-200 h-14">
                            <Input className="border-0 bg-transparent h-full">
                              <InputField
                                placeholder="Confirm your password"
                                value={field.value}
                                onChangeText={field.onChange}
                                secureTextEntry={!showConfirmPassword}
                                autoComplete="new-password"
                                autoCorrect={false}
                                style={{
                                  fontSize: 16,
                                  lineHeight: 20,
                                  height: 56,
                                  paddingHorizontal: 16,
                                  paddingRight: 48,
                                  paddingVertical: 0,
                                  textAlignVertical: 'center',
                                  color: '#111827',
                                  fontWeight: '500',
                                }}
                              />
                              <InputSlot
                                className="absolute right-0 h-full w-14 justify-center items-center"
                                onPress={() =>
                                  setShowConfirmPassword(!showConfirmPassword)
                                }
                              >
                                <InputIcon
                                  as={
                                    showConfirmPassword ? EyeOffIcon : EyeIcon
                                  }
                                  className="text-gray-400"
                                  size="md"
                                />
                              </InputSlot>
                            </Input>
                          </Box>
                          {fieldState.error && (
                            <FormControlError className="mt-2">
                              <FormControlErrorIcon
                                as={AlertCircleIcon}
                                className="text-red-500"
                              />
                              <FormControlErrorText className="text-red-500 font-medium">
                                {fieldState.error?.message}
                              </FormControlErrorText>
                            </FormControlError>
                          )}
                        </FormControl>
                      )}
                    />

                    {/* Continue Button */}
                    <Button
                      className="bg-blue-600 rounded-xl h-14 shadow-sm"
                      onPress={form.handleSubmit(handleNext)}
                      disabled={isLoading || !routerReady}
                    >
                      {isLoading ? (
                        <HStack className="items-center" space="sm">
                          <Spinner size="small" color="white" />
                          <ButtonText className="text-white font-semibold text-base">
                            Loading...
                          </ButtonText>
                        </HStack>
                      ) : (
                        <ButtonText className="text-white font-semibold text-base">
                          Continue
                        </ButtonText>
                      )}
                    </Button>
                  </VStack>
                </Box>

                {/* Sign In Link */}
                <Box className="mt-4">
                  <HStack className="justify-center items-center">
                    <Text className="text-gray-600 font-medium mr-2">
                      Already have an account?
                    </Text>
                    <TouchableOpacity
                      onPress={() => {
                        Keyboard.dismiss();
                        router.push('/sign-in');
                      }}
                    >
                      <Text className="text-blue-600 font-semibold">
                        Sign in
                      </Text>
                    </TouchableOpacity>
                  </HStack>
                </Box>

                {/* Divider */}
                <Box className="flex-row items-center">
                  <Box className="flex-1 h-px bg-gray-200" />
                  <Text className="mx-4 text-gray-500 font-medium">OR</Text>
                  <Box className="flex-1 h-px bg-gray-200" />
                </Box>

                {/* Social Sign Up */}
                <SocialSignIn
                  onGooglePress={handleGoogleSignIn}
                  onApplePress={handleAppleSignIn}
                />
              </VStack>
            </Box>
          </Box>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
