import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';

import { useState } from 'react';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import {
  profileSchema,
  ProfileSchema,
  ProfileSignUpForm,
  signUp,
  uploadMedia,
  useSignUpStore,
} from '@/pages/auth';

import {
  Button,
  ButtonText,
  Box,
  Heading,
  HStack,
  VStack,
  Text,
  Spinner,
} from '@/shared/ui';

import {
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';

const { height: screenHeight } = Dimensions.get('window');

export default function SignUpProfilePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { credentials, profile, setProfile } = useSignUpStore();

  const form = useForm<ProfileSchema>({
    resolver: zodResolver(profileSchema),
    defaultValues: profile,
  });

  // Handle image selection
  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      const imageUri = result.assets[0].uri;
      form.setValue('imageUri', imageUri);
      setProfile({ imageUri });
    }
  };

  // Handle image capture
  const takePhoto = async () => {
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

    if (cameraPermission.status !== 'granted') {
      alert('We need camera permission to take a picture');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      const imageUri = result.assets[0].uri;
      form.setValue('imageUri', imageUri);
      setProfile({ imageUri });
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleSubmit = async (data: ProfileSchema) => {
    try {
      setIsLoading(true);
      setProfile(data);

      let imageRef;

      // Upload image if provided
      if (data.imageUri) {
        // Upload the image directly using the URI
        const result = await uploadMedia(data.imageUri);
        imageRef = result.imageRef;
      }

      // Submit sign-up data
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      const signUpData = {
        email: credentials.email,
        password: credentials.password,
        firstName: data.firstName,
        lastName: data.lastName,
        imageRef,
      };

      const response = await signUp(signUpData);

      // Extract userId from response
      const userId = response.user?.id;

      // Navigate to OTP verification with userId as parameter
      router.push({
        pathname: '/sign-up/otp',
        params: { userId },
      });
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'center',
            minHeight: screenHeight,
            paddingVertical: 40,
          }}
          className="bg-gray-50"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Box className="flex-1 bg-gray-50 justify-center">
            {/* Header */}
            <Box className="px-6 mb-8">
              <VStack className="items-center" space="sm">
                <Heading className="text-3xl font-bold text-gray-900">
                  Create your profile
                </Heading>
                <Text className="text-gray-600 text-center">
                  Tell us a bit about yourself
                </Text>
              </VStack>
            </Box>

            {/* Form Container */}
            <Box className="px-6">
              <VStack space="lg" className="w-full max-w-sm mx-auto">
                {/* Main Form Card */}
                <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <ProfileSignUpForm
                    form={form}
                    onPickImage={pickImage}
                    onTakePhoto={takePhoto}
                  />
                </Box>

                {/* Action Buttons */}
                <VStack space="md">
                  <Button
                    className="bg-blue-600 rounded-xl h-14 shadow-sm"
                    onPress={form.handleSubmit(handleSubmit)}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <HStack className="items-center" space="sm">
                        <Spinner size="small" color="white" />
                        <ButtonText className="text-white font-semibold text-base">
                          Creating account...
                        </ButtonText>
                      </HStack>
                    ) : (
                      <ButtonText className="text-white font-semibold text-base">
                        Continue
                      </ButtonText>
                    )}
                  </Button>

                  <Button
                    onPress={handleBack}
                    variant="outline"
                    className="rounded-xl h-14 border-gray-300 bg-white"
                    disabled={isLoading}
                  >
                    <ButtonText className="text-gray-700 font-semibold text-base">
                      Back
                    </ButtonText>
                  </Button>
                </VStack>
              </VStack>
            </Box>
          </Box>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
