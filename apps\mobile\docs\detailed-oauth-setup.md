# Guide Détaillé pour Obtenir les Identifiants Google OAuth

Ce guide explique en détail comment obtenir les informations spécifiques nécessaires pour configurer l'authentification Google sur différentes plateformes.

## Trouver le Nom du Package pour Android

Le nom du package est l'identifiant unique de votre application Android. Pour une application Expo/React Native, vous pouvez le trouver dans :

1. Ouvre<PERSON> le fichier `app.json` ou `app.config.js` à la racine de votre projet Expo
2. Recherchez la section `android` et la propriété `package`

```json
{
  "expo": {
    "android": {
      "package": "com.votreentreprise.nomapp"
    }
  }
}
```

Si vous n'avez pas encore défini de nom de package, vous pouvez en créer un en suivant la convention de nommage inversée du domaine : `com.votreentreprise.nomapp`

## Obtenir l'Empreinte SHA-1 pour Android

L'empreinte SHA-1 est nécessaire pour l'authentification Google sur Android. Voici comment l'obtenir :

### Pour le Développement (Debug Keystore)

#### Sur Windows :

1. Ouvrez une invite de commande
2. Exécutez la commande suivante :
```
keytool -list -v -keystore %USERPROFILE%\.android\debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### Sur macOS/Linux :

1. Ouvrez un terminal
2. Exécutez la commande suivante :
```
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

3. Recherchez la ligne commençant par "SHA1:" dans la sortie. C'est votre empreinte SHA-1.

### Pour la Production (Release Keystore)

Si vous avez déjà un keystore de production :

```
keytool -list -v -keystore chemin/vers/votre/keystore.jks -alias votre-alias -storepass votre-mot-de-passe
```

### Avec Expo

Si vous utilisez Expo, vous pouvez obtenir l'empreinte SHA-1 avec la commande :

```
expo fetch:android:hashes
```

Ou consultez la documentation Expo sur [comment obtenir les certificats](https://docs.expo.dev/versions/latest/sdk/google/#creating-a-client-id-for-android).

## Trouver le Bundle ID pour iOS

Le Bundle ID est l'identifiant unique de votre application iOS. Pour une application Expo/React Native :

1. Ouvrez le fichier `app.json` ou `app.config.js` à la racine de votre projet Expo
2. Recherchez la section `ios` et la propriété `bundleIdentifier`

```json
{
  "expo": {
    "ios": {
      "bundleIdentifier": "com.votreentreprise.nomapp"
    }
  }
}
```

Si vous n'avez pas encore défini de Bundle ID, vous pouvez en créer un en suivant la même convention que pour Android : `com.votreentreprise.nomapp`

## URI de Redirection pour Expo

Pour Expo, vous devez configurer des URI de redirection spécifiques dans la console Google Cloud. Ces URI suivent un format particulier :

### Pour le Développement

```
https://auth.expo.io/@votre-nom-utilisateur/votre-nom-projet
```

Où :
- `votre-nom-utilisateur` est votre nom d'utilisateur Expo
- `votre-nom-projet` est le nom de votre projet dans app.json

### Pour la Production

Si vous utilisez un domaine personnalisé pour la redirection :

```
votre-schema://votre-domaine
```

Par exemple :
```
com.votreentreprise.nomapp://redirect
```

Vous pouvez configurer ce schéma dans votre fichier app.json :

```json
{
  "expo": {
    "scheme": "com.votreentreprise.nomapp"
  }
}
```

## Étapes Détaillées dans Google Cloud Console

### 1. Créer un Projet

1. Accédez à [Google Cloud Console](https://console.cloud.google.com/)
2. Cliquez sur le sélecteur de projet en haut de la page
3. Cliquez sur "Nouveau projet"
4. Entrez un nom pour votre projet et cliquez sur "Créer"

### 2. Configurer l'Écran de Consentement OAuth

1. Dans le menu de navigation, allez à "APIs & Services" > "OAuth consent screen"
2. Sélectionnez "External" ou "Internal" selon vos besoins
3. Remplissez les informations requises :
   - Nom de l'application
   - Email de support utilisateur
   - Logo (optionnel)
   - Domaine de l'application (optionnel)
   - Informations de contact du développeur
4. Cliquez sur "Save and Continue"
5. Dans la section "Scopes", ajoutez les scopes "email" et "profile"
6. Cliquez sur "Save and Continue"
7. Ajoutez des utilisateurs de test si nécessaire
8. Cliquez sur "Save and Continue"
9. Vérifiez le récapitulatif et cliquez sur "Back to Dashboard"

### 3. Créer les Identifiants OAuth

#### Pour le Web (Web Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "Web application"
4. Donnez un nom à votre client (par exemple, "NeedIt Web")
5. Sous "Authorized JavaScript origins", ajoutez :
   - `http://localhost:3000` (pour le développement)
   - `https://votre-domaine.com` (pour la production, si applicable)
6. Sous "Authorized redirect URIs", ajoutez :
   - `http://localhost:3000/auth/google/callback` (pour le développement)
   - `https://votre-domaine.com/auth/google/callback` (pour la production)
   - `https://auth.expo.io/@votre-nom-utilisateur/votre-nom-projet` (pour Expo)
7. Cliquez sur "Create"
8. Notez le Client ID généré (il sera affiché dans une fenêtre pop-up)

#### Pour Android (Android Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "Android"
4. Donnez un nom à votre client (par exemple, "NeedIt Android")
5. Dans le champ "Package name", entrez le nom du package de votre application (par exemple, `com.votreentreprise.needit`)
6. Dans le champ "SHA-1 certificate fingerprint", entrez l'empreinte SHA-1 que vous avez obtenue précédemment
7. Cliquez sur "Create"
8. Notez le Client ID généré (il sera affiché dans une fenêtre pop-up)

#### Pour iOS (iOS Client ID)

1. Dans le menu de navigation, allez à "APIs & Services" > "Credentials"
2. Cliquez sur "Create Credentials" > "OAuth client ID"
3. Sélectionnez "iOS"
4. Donnez un nom à votre client (par exemple, "NeedIt iOS")
5. Dans le champ "Bundle ID", entrez le Bundle ID de votre application (par exemple, `com.votreentreprise.needit`)
6. Cliquez sur "Create"
7. Notez le Client ID généré (il sera affiché dans une fenêtre pop-up)

### 4. Activer l'API Google Sign-In

1. Dans le menu de navigation, allez à "APIs & Services" > "Library"
2. Recherchez "Google Identity Services" ou "Google Sign-In API"
3. Cliquez sur l'API dans les résultats de recherche
4. Cliquez sur "Enable"

## Configuration dans Votre Application

Une fois que vous avez obtenu tous les identifiants nécessaires, mettez à jour votre fichier `.env` :

```
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_EXPO_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com"
```

## Vérification de la Configuration

Pour vérifier que votre configuration est correcte :

1. Redémarrez votre application
2. Accédez à l'écran de connexion
3. Appuyez sur le bouton "Continue with Google"
4. Vous devriez être redirigé vers l'écran de connexion Google
5. Après avoir sélectionné votre compte, vous devriez être redirigé vers l'application et être connecté

## Ressources Supplémentaires

- [Documentation Google sur l'authentification OAuth 2.0](https://developers.google.com/identity/protocols/oauth2)
- [Guide Expo sur l'authentification Google](https://docs.expo.dev/guides/authentication/#google)
- [Documentation Expo Auth Session](https://docs.expo.dev/versions/latest/sdk/auth-session/)
