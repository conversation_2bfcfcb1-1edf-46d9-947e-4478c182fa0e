import { z } from 'zod';
import { createSelectSchema } from 'drizzle-zod';
import { schema } from '@needit/db';
import { userSelectDto, userUpdateDto } from './user.dto';

// Import other DTOs for timeline data
import { needSelectDto } from '@/modules/needs/need.dto';
import { offerSelectDto } from '@/modules/offers/offer.dto';

// Create message select DTO since it's not exported from the messages module
const messageSelectDto = createSelectSchema(schema.messages);
type MessageSelectDto = z.infer<typeof messageSelectDto>;

// Profile response DTO
export const profileResponseDto = userSelectDto.extend({
  // Add any additional profile-specific fields here
});
export type ProfileResponseDto = z.infer<typeof profileResponseDto>;

// Profile update DTO - reusing the user update DTO
export const profileUpdateDto = userUpdateDto;
export type ProfileUpdateDto = z.infer<typeof profileUpdateDto>;

// Profile picture update DTO
export const profilePictureUpdateDto = z.object({
  imageRef: z.string().min(1),
});
export type ProfilePictureUpdateDto = z.infer<typeof profilePictureUpdateDto>;

// Profile stats response DTO
export const profileStatsResponseDto = z.object({
  needsCount: z.number(),
  offersCount: z.number(),
  messagesCount: z.number(),
});
export type ProfileStatsResponseDto = z.infer<typeof profileStatsResponseDto>;

// Recent needs response DTO
export const recentNeedsResponseDto = z.array(needSelectDto);
export type RecentNeedsResponseDto = z.infer<typeof recentNeedsResponseDto>;

// Recent offers response DTO
export const recentOffersResponseDto = z.array(offerSelectDto);
export type RecentOffersResponseDto = z.infer<typeof recentOffersResponseDto>;

// Recent messages response DTO
export const recentMessagesResponseDto = z.array(messageSelectDto);
export type RecentMessagesResponseDto = z.infer<typeof recentMessagesResponseDto>;

// Activity timeline item DTO
export const activityTimelineItemDto = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('need'),
    data: needSelectDto,
    createdAt: z.date(),
  }),
  z.object({
    type: z.literal('offer'),
    data: offerSelectDto,
    createdAt: z.date(),
  }),
  z.object({
    type: z.literal('message'),
    data: messageSelectDto,
    createdAt: z.date(),
  }),
]);
export type ActivityTimelineItemDto = z.infer<typeof activityTimelineItemDto>;

// Activity timeline response DTO
export const activityTimelineResponseDto = z.array(activityTimelineItemDto);
export type ActivityTimelineResponseDto = z.infer<typeof activityTimelineResponseDto>;
