import { Hono } from 'hono';
import { setC<PERSON>ie } from 'hono/cookie';
import { HTTPException } from 'hono/http-exception';
import { getConnInfo } from '@hono/node-server/conninfo';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import status from 'http-status';

import {
  defaultSessionDuration,
  sessionTokenCookieName,
  sessionTokenCookieOptions,
} from '@/modules/auth/auth.config';
import { hashSessionToken } from '@/modules/auth/auth.lib';
import { sessionService } from '@/modules/auth/services/session.service';
import { userService } from '@/modules/users';

import { generateToken, getDateFromNow } from '@/shared/lib';
import type { AuthVariables } from '@/shared/model';

// Schema for Google authentication
const googleAuthSchema = z.object({
  accessToken: z.string(),
  userInfo: z.object({
    id: z.string(),
    email: z.string().email(),
    name: z.string().optional(),
    given_name: z.string().optional(),
    family_name: z.string().optional(),
    picture: z.string().url().optional(),
  }),
});

export const socialAuthRoutes = new Hono<{
  Variables: AuthVariables;
}>();

socialAuthRoutes.post('/google', zValidator('json', googleAuthSchema), async (c) => {
  const body = c.req.valid('json');
  const { userInfo } = body;

  try {
    // Check if user exists
    let user = await userService.getUserByEmail(userInfo.email);

    // If user doesn't exist, create a new one
    if (!user) {
      user = await userService.createUser({
        email: userInfo.email,
        firstName: userInfo.given_name || userInfo.name?.split(' ')[0] || '',
        lastName: userInfo.family_name || userInfo.name?.split(' ').slice(1).join(' ') || '',
        imageRef: userInfo.picture,
      });
    }

    // Create a new session
    const userAgent = c.req.header('User-Agent');
    const clientIp = c.req.header('X-Forwarded-For') || getConnInfo(c).remote.address;

    const sessionToken = generateToken();
    const hashedToken = hashSessionToken(sessionToken);

    const expirationDate = getDateFromNow(defaultSessionDuration);

    const session = await sessionService.createSession({
      tokenHash: hashedToken,
      userId: user.id,
      ipAddress: clientIp,
      userAgent,
      expiresAt: expirationDate,
    });

    // Set session cookie
    setCookie(c, sessionTokenCookieName, sessionToken, {
      ...sessionTokenCookieOptions,
      expires: session.expiresAt,
    });

    return c.json({
      message: 'Google authentication successful',
      session,
    });
  } catch (error) {
    console.error('Google authentication error:', error);
    throw new HTTPException(status.INTERNAL_SERVER_ERROR, {
      res: Response.json({
        error: {
          code: status[`${status.INTERNAL_SERVER_ERROR}_NAME`],
          message: 'Failed to authenticate with Google',
        },
      }),
    });
  }
});
