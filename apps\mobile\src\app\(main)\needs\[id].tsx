import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import {
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {
  ChevronLeft,
  Share2,
  Clock,
  MapPin,
  Star,
  Flag,
  ArrowRight,
  Euro,
  Tag,
  Zap,
  Apple,
  Dog,
  Car,
  Hammer,
  ShoppingBag,
  Home,
  Leaf,
  Laptop,
  Heart,
  Baby,
  GraduationCap,
  Briefcase,
  Shirt,
  Music,
  Camera,
  Gamepad2,
  Wrench,
  Paintbrush,
  Users,
  Globe,
} from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';

import { useNeedStore } from '@/shared/model';
import { CreateOfferDialog } from '@/pages/needs';
import { getCategories } from '@/pages/needs';
import {
  Avatar,
  AvatarFallbackText,
  AvatarImage,
  Box,
  Heading,
  HStack,
  VStack,
  Divider,
  Spinner,
} from '@/shared/ui';

const NeedDetail = () => {
  const { id: needId } = useLocalSearchParams();
  const router = useRouter();
  const {
    selectedNeed,
    fetchNeedDetail,
    isLoading: isLoadingNeed,
  } = useNeedStore();

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['categories'],
    queryFn: getCategories,
  });

  useEffect(() => {
    if (needId) {
      fetchNeedDetail(needId as string);
    }
  }, [needId, fetchNeedDetail]);

  const dateStringToLocaleString = (dateString: string) => {
    // Display date as "time ago"
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours} h`;
    } else if (minutes > 0) {
      return `${minutes} min`;
    } else {
      return 'just now';
    }
  };

  // Function to get category name from ID
  const getCategoryName = (categoryId: string | undefined) => {
    if (!categoryId || !categories) return 'General';
    const category = categories.find((cat) => cat.id === categoryId);
    return category?.name || 'General';
  };

  // Fonction pour obtenir l'image selon la catégorie
  const getCategoryImage = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop&auto=format';
      case 'animals':
      case 'animaux':
      case 'pets':
        return 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=800&h=600&fit=crop&auto=format';
      case 'transport':
      case 'transportation':
        return 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop&auto=format';
      case 'diy':
      case 'bricolage':
      case 'repairs':
      case 'réparations':
        return 'https://images.unsplash.com/photo-1581783898377-1dcd8e5f0935?w=800&h=600&fit=crop&auto=format';
      case 'shopping':
      case 'courses':
      case 'groceries':
        return 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=800&h=600&fit=crop&auto=format';
      case 'cleaning':
      case 'ménage':
      case 'housework':
        return 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop&auto=format';
      case 'gardening':
      case 'jardinage':
      case 'garden':
        return 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=800&h=600&fit=crop&auto=format';
      case 'technology':
      case 'gaming':
      case 'technologie':
      case 'tech':
        return 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop&auto=format';
      case 'health':
      case 'santé':
      case 'medical':
        return 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop&auto=format';
      case 'childcare':
      case "garde d'enfants":
      case 'babysitting':
        return 'https://images.unsplash.com/photo-1476703993599-0035a21b17a9?w=800&h=600&fit=crop&auto=format';
      case 'education':
      case 'éducation':
      case 'learning':
      case 'tutoring':
        return 'https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=800&h=600&fit=crop&auto=format';
      case 'work':
      case 'travail':
      case 'professional':
        return 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=600&fit=crop&auto=format';
      case 'clothing':
      case 'vêtements':
      case 'fashion':
        return 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800&h=600&fit=crop&auto=format';
      case 'music':
      case 'musique':
      case 'instruments':
        return 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop&auto=format';
      case 'photography':
      case 'photographie':
      case 'photo':
        return 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=800&h=600&fit=crop&auto=format';
      case 'art':
      case 'arts':
      case 'creative':
        return 'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=800&h=600&fit=crop&auto=format';
      case 'community':
      case 'communauté':
      case 'social':
        return 'https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=800&h=600&fit=crop&auto=format';
      case 'events':
      case 'événements':
      case 'party':
        return 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop&auto=format';
      default:
        return 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=600&fit=crop&auto=format';
    }
  };

  // Fonction pour obtenir le style et l'icône selon la catégorie
  const getCategoryStyle = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'food':
      case 'alimentaire':
        return {
          icon: <Apple size={24} color="#10B981" />,
          backgroundColor: '#10B981',
          backgroundLight: '#DCFCE7',
        };
      case 'animals':
      case 'animaux':
      case 'pets':
        return {
          icon: <Dog size={24} color="#8B5CF6" />,
          backgroundColor: '#8B5CF6',
          backgroundLight: '#F3E8FF',
        };
      case 'transport':
      case 'transportation':
        return {
          icon: <Car size={24} color="#3B82F6" />,
          backgroundColor: '#3B82F6',
          backgroundLight: '#DBEAFE',
        };
      case 'diy':
      case 'bricolage':
      case 'repairs':
      case 'réparations':
        return {
          icon: <Hammer size={24} color="#F59E0B" />,
          backgroundColor: '#F59E0B',
          backgroundLight: '#FEF3C7',
        };
      case 'shopping':
      case 'courses':
      case 'groceries':
        return {
          icon: <ShoppingBag size={24} color="#EF4444" />,
          backgroundColor: '#EF4444',
          backgroundLight: '#FEE2E2',
        };
      case 'cleaning':
      case 'ménage':
      case 'housework':
        return {
          icon: <Home size={24} color="#06B6D4" />,
          backgroundColor: '#06B6D4',
          backgroundLight: '#CFFAFE',
        };
      case 'gardening':
      case 'jardinage':
      case 'garden':
        return {
          icon: <Leaf size={24} color="#84CC16" />,
          backgroundColor: '#84CC16',
          backgroundLight: '#ECFCCB',
        };
      case 'technology':
      case 'gaming':
      case 'technologie':
      case 'tech':
        return {
          icon: <Laptop size={24} color="#6366F1" />,
          backgroundColor: '#6366F1',
          backgroundLight: '#E0E7FF',
        };
      case 'health':
      case 'santé':
      case 'medical':
        return {
          icon: <Heart size={24} color="#EF4444" />,
          backgroundColor: '#EF4444',
          backgroundLight: '#FEE2E2',
        };
      case 'childcare':
      case "garde d'enfants":
      case 'babysitting':
        return {
          icon: <Baby size={24} color="#F59E0B" />,
          backgroundColor: '#F59E0B',
          backgroundLight: '#FEF3C7',
        };
      case 'education':
      case 'éducation':
      case 'learning':
      case 'tutoring':
        return {
          icon: <GraduationCap size={24} color="#8B5CF6" />,
          backgroundColor: '#8B5CF6',
          backgroundLight: '#F3E8FF',
        };
      case 'work':
      case 'travail':
      case 'professional':
        return {
          icon: <Briefcase size={24} color="#1F2937" />,
          backgroundColor: '#1F2937',
          backgroundLight: '#F3F4F6',
        };
      case 'clothing':
      case 'vêtements':
      case 'fashion':
        return {
          icon: <Shirt size={24} color="#EC4899" />,
          backgroundColor: '#EC4899',
          backgroundLight: '#FCE7F3',
        };
      case 'music':
      case 'musique':
      case 'instruments':
        return {
          icon: <Music size={24} color="#7C3AED" />,
          backgroundColor: '#7C3AED',
          backgroundLight: '#EDE9FE',
        };
      case 'photography':
      case 'photographie':
      case 'photo':
        return {
          icon: <Camera size={24} color="#059669" />,
          backgroundColor: '#059669',
          backgroundLight: '#D1FAE5',
        };
      case 'art':
      case 'arts':
      case 'creative':
        return {
          icon: <Paintbrush size={24} color="#DC2626" />,
          backgroundColor: '#DC2626',
          backgroundLight: '#FEE2E2',
        };
      case 'community':
      case 'communauté':
      case 'social':
        return {
          icon: <Users size={24} color="#0891B2" />,
          backgroundColor: '#0891B2',
          backgroundLight: '#CFFAFE',
        };
      case 'events':
      case 'événements':
      case 'party':
        return {
          icon: <Globe size={24} color="#7C2D12" />,
          backgroundColor: '#7C2D12',
          backgroundLight: '#FED7AA',
        };
      default:
        return {
          icon: <Zap size={24} color="#6B7280" />,
          backgroundColor: '#6B7280',
          backgroundLight: '#F3F4F6',
        };
    }
  };

  if (isLoadingNeed) {
    return (
      <Box className="flex-1 bg-gray-50 justify-center items-center">
        <Spinner size="large" className="text-blue-600" />
        <Text className="mt-4 text-gray-600 font-medium">
          Loading need details...
        </Text>
      </Box>
    );
  }

  // Obtenir le nom de la catégorie, l'image et le style correspondants
  const categoryName = getCategoryName(selectedNeed?.categoryId);
  const categoryImage = getCategoryImage(categoryName);
  const categoryStyle = getCategoryStyle(categoryName);

  return (
    <Box className="flex-1 bg-gray-50">
      {/* Back Button */}
      <Box className="absolute top-14 left-4 z-20">
        <TouchableOpacity
          onPress={() => router.back()}
          className="w-10 h-10 bg-white/90 backdrop-blur rounded-xl items-center justify-center shadow-sm"
        >
          <ChevronLeft size={20} color="#6B7280" />
        </TouchableOpacity>
      </Box>

      <ScrollView
        className="flex-1 px-6 pt-32"
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'ios' ? 200 : 180, // Encore plus d'espace
        }}
        showsVerticalScrollIndicator={false}
        bounces={true}
        alwaysBounceVertical={true}
      >
        {/* Title Card SANS icônes */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Heading className="text-2xl font-bold text-gray-900 mb-4">
            {selectedNeed?.title || 'Need Details'}
          </Heading>

          <HStack className="justify-between items-center">
            <HStack space="sm" className="items-center">
              <Box className="w-8 h-8 bg-green-100 rounded-lg items-center justify-center">
                <Clock size={16} color="#10B981" />
              </Box>
              <Text className="text-gray-600 font-medium">
                {selectedNeed?.createdAt
                  ? dateStringToLocaleString(selectedNeed.createdAt)
                  : 'Unknown date'}
              </Text>
            </HStack>

            <HStack space="sm" className="items-center">
              <Box className="w-8 h-8 bg-blue-100 rounded-lg items-center justify-center">
                <MapPin size={16} color="#3B82F6" />
              </Box>
              <Text className="text-gray-600 font-medium">Paris, France</Text>
            </HStack>
          </HStack>
        </Box>

        {/* Main Image Card - Avec image automatique basée sur la catégorie */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
          <Box className="w-full aspect-video bg-gray-200 relative">
            <Image
              source={{
                uri: categoryImage,
              }}
              className="w-full h-full"
              resizeMode="cover"
              onError={(error) => {
                console.log("Erreur de chargement d'image:", error);
              }}
              onLoad={() => {
                console.log('Image chargée avec succès');
              }}
            />
            {/* Badge de catégorie avec icône sur l'image */}
            <Box className="absolute bottom-4 left-4">
              <Box className="bg-black/70 backdrop-blur px-3 py-1 rounded-full flex-row items-center">
                {React.cloneElement(categoryStyle.icon, {
                  size: 16,
                  color: 'white',
                })}
                <Text className="text-white text-sm font-medium ml-2">
                  {categoryName}
                </Text>
              </Box>
            </Box>
            {/* Overlay buttons */}
            <Box className="absolute top-4 right-4">
              <HStack space="sm">
                <TouchableOpacity className="w-10 h-10 bg-white/90 backdrop-blur rounded-xl items-center justify-center shadow-sm">
                  <Flag size={18} color="#EF4444" />
                </TouchableOpacity>
                <TouchableOpacity className="w-10 h-10 bg-white/90 backdrop-blur rounded-xl items-center justify-center shadow-sm">
                  <Share2 size={18} color="#3B82F6" />
                </TouchableOpacity>
              </HStack>
            </Box>
          </Box>
        </Box>

        {/* Description Card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Description
          </Text>
          <Text className="text-gray-700 leading-relaxed text-base">
            {selectedNeed?.description || 'No description provided.'}
          </Text>
        </Box>

        {/* Need Details Card avec icône de catégorie */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Need Information
          </Text>
          <HStack space="lg" className="justify-center">
            <VStack className="items-center" space="xs">
              <Box className="w-12 h-12 bg-blue-100 rounded-xl items-center justify-center">
                <Euro size={24} color="#3B82F6" strokeWidth={1.5} />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                Free
              </Text>
            </VStack>
            <VStack className="items-center" space="xs">
              <Box
                className="w-12 h-12 rounded-xl items-center justify-center"
                style={{ backgroundColor: categoryStyle.backgroundLight }}
              >
                {categoryStyle.icon}
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                {categoryName}
              </Text>
            </VStack>
            <VStack className="items-center" space="xs">
              <Box className="w-12 h-12 bg-amber-100 rounded-xl items-center justify-center">
                <Zap size={24} color="#F59E0B" strokeWidth={1.5} />
              </Box>
              <Text className="text-gray-700 text-xs text-center font-medium">
                Urgent
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* User Profile Card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Posted By
          </Text>
          <HStack space="lg" className="items-center">
            <Avatar size="lg">
              <AvatarFallbackText>
                {`${selectedNeed?.user?.firstName || ''} ${
                  selectedNeed?.user?.lastName || ''
                }`}
              </AvatarFallbackText>
              <AvatarImage
                source={{
                  uri: 'https://static.animaute.fr/upload/img/animhote/max/10929412341643236116.webp',
                }}
              />
            </Avatar>

            <VStack className="flex-1" space="xs">
              <Heading className="text-lg font-bold text-gray-900">
                {`${selectedNeed?.user?.firstName || ''} ${
                  selectedNeed?.user?.lastName || ''
                }`}
              </Heading>
              <Text className="text-gray-500 font-medium">Paris, France</Text>
              <HStack space="xs" className="items-center">
                <Star size={16} fill="#3B82F6" color="#3B82F6" />
                <Text className="text-gray-900">
                  <Text className="font-bold">4.9</Text>
                  <Text className="text-gray-500"> (10 reviews)</Text>
                </Text>
              </HStack>
            </VStack>

            <TouchableOpacity className="bg-blue-50 px-4 py-2 rounded-xl">
              <HStack space="xs" className="items-center">
                <Text className="text-blue-600 font-semibold">
                  View Profile
                </Text>
                <ArrowRight size={14} color="#3B82F6" />
              </HStack>
            </TouchableOpacity>
          </HStack>
        </Box>

        {/* Stats Card - dernière card */}
        <Box className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <Text className="text-sm font-bold text-gray-700 mb-4 uppercase tracking-wider">
            Helper Stats
          </Text>
          <HStack className="justify-between">
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-blue-600">50</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Needs Helped
              </Text>
            </VStack>
            <Box className="w-px bg-gray-200 mx-4 self-stretch" />
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-green-600">5 min</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Avg Response
              </Text>
            </VStack>
            <Box className="w-px bg-gray-200 mx-4 self-stretch" />
            <VStack className="items-center flex-1" space="xs">
              <Text className="text-2xl font-bold text-purple-600">98%</Text>
              <Text className="text-gray-600 text-sm text-center font-medium">
                Success Rate
              </Text>
            </VStack>
          </HStack>
        </Box>
      </ScrollView>

      <CreateOfferDialog needId={needId as string} />
    </Box>
  );
};

export default NeedDetail;
