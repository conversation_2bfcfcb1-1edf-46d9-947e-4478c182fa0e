import { z } from 'zod';

// DTO pour création d'une proposal (version simplifiée temporaire)
export const meetingProposalCreateDto = z.object({
  offerId: z.string().uuid(),
  receiverId: z.string().uuid(),
  proposedMeetingTime: z.string().transform((str) => new Date(str)),
  message: z.string().optional(),
  expiresAt: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
});

// DTO pour mise à jour du statut
export const meetingProposalUpdateDto = z.object({
  status: z.enum(['accepted', 'declined']),
});

// DTO pour la réponse (version simplifiée temporaire)
export const meetingProposalSelectDto = z.object({
  id: z.string().uuid(),
  offerId: z.string().uuid(),
  proposerId: z.string().uuid(),
  receiverId: z.string().uuid(),
  proposedMeetingTime: z.date(),
  message: z.string().nullable(),
  status: z.enum(['pending', 'accepted', 'declined', 'expired']),
  qrCodeData: z.string().nullable(),
  acceptedAt: z.date().nullable(),
  declinedAt: z.date().nullable(),
  expiresAt: z.date().nullable(),
  createdAt: z.date(),
  updatedAt: z.date().nullable(),
});

export type MeetingProposalCreateDto = z.infer<typeof meetingProposalCreateDto>;
export type MeetingProposalUpdateDto = z.infer<typeof meetingProposalUpdateDto>;
