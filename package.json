{"name": "@needit/source", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "pnpm run dev:api & pnpm run dev:mobile", "dev:api": "pnpm --filter api dev", "dev:mobile": "pnpm --filter mobile start", "build": "pnpm exec nx run-many --target=build", "build:api": "pnpm --filter api build", "start:api": "pnpm --filter api start", "test": "pnpm --filter api test", "test:api": "pnpm --filter api test", "lint": "pnpm --filter api lint && pnpm --filter mobile lint", "db:push": "pnpm --filter db db:push", "db:studio": "pnpm --filter db db:studio"}, "private": true, "devDependencies": {"@eslint/js": "^9.8.0", "@nx/eslint": "20.4.0", "@nx/eslint-plugin": "20.4.0", "@nx/js": "20.4.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react-native": "^13.2.0", "@types/node": "18.16.9", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-expo": "^52.0.6", "nx": "20.4.0", "prettier": "^2.6.2", "react-native-maps": "^1.18.0", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "zustand": "^5.0.3"}, "dependencies": {}}